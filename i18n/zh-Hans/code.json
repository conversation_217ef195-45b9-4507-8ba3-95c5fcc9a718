{"theme.ErrorPageContent.title": {"message": "页面已崩溃。", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "回到顶部", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "历史博文", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "历史博文", "description": "The page & hero description of the blog archive page"}, "theme.blog.post.paginator.navAriaLabel": {"message": "博文分页导航", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "较新一篇", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "较旧一篇", "description": "The blog post button label to navigate to the older/next post"}, "theme.blog.paginator.navAriaLabel": {"message": "博文列表分页导航", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "较新的博文", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "较旧的博文", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.colorToggle.ariaLabel": {"message": "切换浅色/暗黑模式（当前为{mode}）", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "暗黑模式", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "浅色模式", "description": "The name for the light color mode"}, "theme.tags.tagsPageLink": {"message": "查看所有标签", "description": "The label of the link targeting the tag list page"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "页面路径", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "{count} 个项目", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "文件选项卡", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "上一页", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "下一页", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "{count} 篇文档带有标签", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged}「{tagName}」", "description": "The title of the page for a docs tag"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版尚未发行的文档。", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版的文档，现已不再积极维护。", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "最新的文档请参阅 {latestVersionLink} ({versionLabel})。", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "最新版本", "description": "The label used for the latest version suggestion link label"}, "theme.docs.versionBadge.label": {"message": "版本：{versionLabel}"}, "theme.common.editThisPage": {"message": "编辑此页", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "{heading}的直接链接", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": "于 {date} ", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": "由 {user} ", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "最后{byUser}{atDate}更新", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "选择版本", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.NotFound.title": {"message": "找不到页面", "description": "The title of the 404 page"}, "theme.tags.tagsListLabel": {"message": "标签：", "description": "The label alongside a tag list"}, "theme.admonition.caution": {"message": "警告", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "危险", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "信息", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "备注", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "提示", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "注意", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "关闭", "description": "The ARIA label for close button of announcement bar"}, "theme.blog.sidebar.navAriaLabel": {"message": "最近博文导航", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.wordWrapToggle": {"message": "切换自动换行", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.CodeBlock.copied": {"message": "复制成功", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "复制代码到剪贴板", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "复制", "description": "The copy button label on code blocks"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "展开侧边栏分类 '{label}'", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "折叠侧边栏分类 '{label}'", "description": "The ARIA label to collapse the sidebar category"}, "theme.NotFound.p1": {"message": "我们找不到您要找的页面。", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "请联系原始链接来源网站的所有者，并告知他们链接已损坏。", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "本页总览", "description": "The label used by the button on the collapsible TOC component"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "选择语言", "description": "The label for the mobile language switcher dropdown"}, "theme.NavBar.navAriaLabel": {"message": "主导航", "description": "The ARIA label for the main navigation"}, "theme.blog.post.readMore": {"message": "阅读更多", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "阅读 {title} 的全文", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "阅读需 {readingTime} 分钟", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "主页面", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.navAriaLabel": {"message": "文档侧边栏", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "关闭导航栏", "description": "The ARIA label for close button of mobile sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← 回到主菜单", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "切换导航栏", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.docs.sidebar.expandButtonTitle": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.blog.post.plurals": {"message": "{count} 篇博文", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} 含有标签「{tagName}」", "description": "The title of the page for a blog tag"}, "theme.blog.author.pageTitle": {"message": "{authorName} - {nPosts}", "description": "The title of the page for a blog author"}, "theme.blog.authorsList.pageTitle": {"message": "Authors", "description": "The title of the authors page"}, "theme.blog.authorsList.viewAll": {"message": "View All Authors", "description": "The label of the link targeting the blog authors page"}, "theme.blog.author.noPosts": {"message": "This author has not written any posts yet.", "description": "The text for authors with 0 blog post"}, "theme.contentVisibility.unlistedBanner.title": {"message": "未列出页", "description": "The unlisted content banner title"}, "theme.contentVisibility.unlistedBanner.message": {"message": "此页面未列出。搜索引擎不会对其索引，只有拥有直接链接的用户才能访问。", "description": "The unlisted content banner message"}, "theme.contentVisibility.draftBanner.title": {"message": "Draft page", "description": "The draft content banner title"}, "theme.contentVisibility.draftBanner.message": {"message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "description": "The draft content banner message"}, "theme.ErrorPageContent.tryAgain": {"message": "重试", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "跳到主要内容", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "标签", "description": "The title of the tag list page"}}