import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import HomepageFeatures from '@site/src/components/HomepageFeatures';

import Heading from '@theme/Heading';
import styles from './index.module.css';

function HomepageHeader() {
  const {siteConfig} = useDocusaurusContext();
  return (
    <header className={clsx('hero hero--primary', styles.heroBanner)}>
      <div className="container">
        <Heading as="h1" className="hero__title">
          {siteConfig.title}
        </Heading>
        <p className="hero__subtitle">{siteConfig.tagline}</p>
        <div className={styles.buttons}>
          <Link
            className="button button--secondary button--lg"
            to="/docs/intro">
            XBIT去中心化交易所阐述 - 5min ⏱️
          </Link>
        </div>
      </div>
    </header>
  );
}

export default function Home() {
  const {siteConfig} = useDocusaurusContext();
  return (
    <Layout
      title={`Hello from ${siteConfig.title}`}
      description="Description will go into a meta tag in <head />">
      <HomepageHeader />
      <main>
        <div className="container padding-vert--xl">
          <div className="row">
            <div className="col col--4 margin-vert--lg">
              <Link className="card card--link shadow--md padding--lg" to="/internal/">
                <div className="card__header">
                  <h3>内部文档</h3>
                </div>
                <div className="card__body">
                  <p>查看内部文档</p>
                </div>
              </Link>
            </div>
            <div className="col col--4 margin-vert--lg">
              <Link className="card card--link shadow--md padding--lg" to="/docs/">
                <div className="card__header">
                  <h3>接口文档</h3>
                </div>
                <div className="card__body">
                  <p>查看 API 文档</p>
                </div>
              </Link>
            </div>
            <div className="col col--4 margin-vert--lg">
              <Link className="card card--link shadow--md padding--lg" to="/hypertrader/">
                <div className="card__header">
                  <h3>HyperTrader 文档</h3>
                </div>
                <div className="card__body">
                  <p>查看 HyperTrader 文档</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
        <HomepageFeatures />
      </main>
    </Layout>
  );
}
