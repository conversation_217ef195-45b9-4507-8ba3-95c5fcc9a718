// @ts-check
// `@type` JSDoc annotations allow editor autocompletion and type checking
// (when paired with `@ts-check`).
// There are various equivalent ways to declare your Docusaurus config.
// See: https://docusaurus.io/docs/api/docusaurus-config

import {themes as prismThemes} from 'prism-react-renderer';


const config = {
  title: 'XBIT去中心化交易所',
  tagline: '官方系统文档',
  favicon: 'img/favicon.ico',
  url: 'https://unstable-dex-docs.xbit.live',
  baseUrl: '/xbit/',
  organizationName: 'xbit-dex',
  projectName: 'documentation',
  trailingSlash: false,


  onBrokenLinks: 'warn',
  onBrokenMarkdownLinks: 'warn',

  i18n: {
    defaultLocale: 'zh-Hans',
    locales: ['zh-Hans', 'en'],
  },

  presets: [
    [
      'classic',
      /** @type {import('@docusaurus/preset-classic').Options} */
      ({
        docs: false,
        blog: false,
        theme: {
          customCss: './src/css/custom.css',
        },
      }),
    ],
  ],

  plugins: [
    // 对外文档插件实例
    [
      '@docusaurus/plugin-content-docs',
      {
        id: 'hypertrader',
        path: 'hypertrader-docs',
        routeBasePath: 'hypertrader',
        sidebarPath: require.resolve('./sidebars.hypertrader.js'),
        editUrl: 'https://gitlab.xbit.live/xbit/xbit-dex/documentation', // 根据需要配置
      },
    ],
    // 对外文档插件实例
    [
      '@docusaurus/plugin-content-docs',
      {
        id: 'external',
        path: 'external-docs',
        routeBasePath: 'docs',
        sidebarPath: require.resolve('./sidebars.external.js'),
        editUrl: 'https://gitlab.xbit.live/xbit/xbit-dex/documentation', // 根据需要配置
      },
    ],
    // 内部文档插件实例
    [
      '@docusaurus/plugin-content-docs',
      {
        id: 'internal',
        path: 'internal-docs',
        routeBasePath: 'internal',
        sidebarPath: require.resolve('./sidebars.internal.js'),
        editUrl: 'https://gitlab.xbit.live/xbit/xbit-dex/documentation', // 根据需要配置
      },
    ],
  ],

  themeConfig:
    /** @type {import('@docusaurus/preset-classic').ThemeConfig} */
    ({
      // Replace with your project's social card
      image: 'img/docusaurus-social-card.jpg',
      navbar: {
        title: 'XBIT去中心化交易所',
        logo: {
          alt: 'XBIT',
          src: 'img/xbit-logo.png',
        },
        items: [
          {
            label: '内部文档',
            href: '/internal.html',
            position: 'left',
            prefetch: false,
            target: '_blank', 
          },
          {
            label: 'XBIT去中心化交易所',
            to: '/docs/',
            position: 'left',
          },
          // {
          //   label: 'HyperTrader',
          //   to: '/hypertrader/',
          //   position: 'left',
          // },
          {
            type: 'localeDropdown',
            position: 'right',
          },
          {
            href: 'https://gitlab.xbit.live/xbit/xbit-dex/documentation',
            label: 'Gitlab',
            position: 'right',
          },
        ],
      },
      footer: {
        style: 'dark',
        links: [
          {
            title: 'Docs',
            items: [
              {
                label: 'Tutorial',
                to: '/docs/intro',
              },
            ],
          },
          {
            title: 'Community',
            items: [
              {
                label: 'Stack Overflow',
                href: 'https://stackoverflow.com/questions/tagged/xbit',
              },
              {
                label: 'Discord',
                href: 'https://discordapp.com/invite/xbit',
              },
              {
                label: 'X',
                href: 'https://x.com/xbit',
              },
            ],
          },
          {
            title: 'More',
            items: [
              {
                label: 'Blog',
                to: '/blog',
              },
              {
                label: 'GitHub',
                href: 'https://github.com/xbit-dex',
              },
            ],
          },
        ],
        copyright: `Copyright © ${new Date().getFullYear()} XBIT, Inc. Built with Docusaurus.`,
      },
      prism: {
        theme: prismThemes.github,
        darkTheme: prismThemes.dracula,
      },
    }),
};

export default config;
