name: Deploy Docusaurus to GitHub Pages

on:
  push:
    branches: [ master ]

permissions:
  contents: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Install dependencies
        run: npm ci

      - name: Build & Deploy to GitHub Pages
        env:
          GIT_USER: "xbit-dex"
          GIT_PASS: ${{ secrets.GITHUB_TOKEN }}
          GIT_USER_NAME: "github-actions[bot]"
          GIT_USER_EMAIL: "41898282+github-actions[bot]@users.noreply.github.com"
        run: npm run deploy
