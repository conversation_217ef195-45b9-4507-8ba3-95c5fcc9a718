# HyperLiquid WebSocket API

# HyperLiquid WebSocket API 文档

本文件汇总了用于实时数据流的 WebSocket 端点和订阅示例。

## 目录

- [基础 WebSocket 端点](#基础-websocket-端点)
- [订阅类型](#订阅类型)
  - [所有市场标识符 (allMids)](#所有市场标识符-allmids)
  - [最佳买卖价 (BBO)](#最佳买卖价-bbo)
  - [K 线数据 (Candle)](#k-线数据-candle)
  - [区块通知 (Explorer Block)](#区块通知-explorer-block)

---

## 基础 WebSocket 端点

| 端点                           | 描述                               |
|--------------------------------|------------------------------------|
| `wss://api.hyperliquid.xyz/ws` | 市场数据订阅主端点                 |
| `wss://rpc.hyperliquid.xyz/ws` | 区块链浏览器事件的 RPC 端点        |

---

## 订阅类型

### 所有市场标识符 (allMids)

订阅以接收所有市场标识符及相关元数据。

**端点：** `wss://api.hyperliquid.xyz/ws`

```json
{
  "method": "subscribe",
  "subscription": ["allMids"]
}
```

---

### 最佳买卖价 (BBO)

订阅指定币种的实时最佳买入和卖出报价。

**端点：** `wss://api.hyperliquid.xyz/ws`

```json
{
  "method": "subscribe",
  "subscription": {
    "type": "bbo",
    "coin": "BTC"
  }
}
```

---

### K 线数据 (Candle)

订阅指定币种在特定时间间隔内的实时 K 线更新。

**端点：** `wss://api.hyperliquid.xyz/ws`

```json
{
  "method": "subscribe",
  "subscription": {
    "type": "candle",
    "coin": "BTC",
    "interval": "1m"
  }
}
```

---

### 区块通知 (Explorer Block)

订阅 HyperLiquid 浏览器检测到的新区块通知。

**端点：** `wss://rpc.hyperliquid.xyz/ws`

```json
{
  "method": "subscribe",
  "subscription": {
    "type": "explorerBlock"
  }
}
```