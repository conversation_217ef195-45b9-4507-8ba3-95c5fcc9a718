# HyperLiquid REST API

# HyperLiquid REST API Documentation

本文档提供了 HyperLiquid REST 端点的全面参考，包括请求方法、URL、标头和示例主体。

## Table of Contents

- [Info Endpoints](#info-endpoints)
  - [未成交订单](#未成交订单)
  - [前端未成交订单](#前端未成交订单)
  - [已成交订单](#已成交订单)
  - [帐号明细/资金仓位等](#帐号明细资金仓位等)
  - [合约明细](#合约明细)
  - [交易所明细](#交易所明细)
  - [时间区间内用户订单历史](#时间区间内用户订单历史)
  - [时间区间内金库订单历史](#时间区间内金库订单历史)
  - [BTC 15分钟K线 快照](#btc-15分钟k线-快照)
  - [子帐号查询](#子帐号查询)
  - [帐号费率查询](#帐号费率查询)
  - [现货明细](#现货明细)
  - [代理明细](#代理明细)
  - [法律合同认可明细？](#法律合同认可明细)
  - [创建金库](#创建金库)
  - [用户金库锁仓信息](#用户金库锁仓信息)
  - [策略员金库明细](#策略员金库明细)
- [Exchange Endpoints](#exchange-endpoints)
  - [金库下单做多](#金库下单做多)
  - [金库订单设置止损](#金库订单设置止损)

---

## Info Endpoints

### 未成交订单

**Method:** `POST`  
**URL:** `https://api.hyperliquid.xyz/info`  

**Headers:**
```http
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "openOrders",
  "user": "******************************************"
}
```

---

### 前端未成交订单

**Method:** `POST`  
**URL:** `https://api.hyperliquid.xyz/info`  

**Headers:**
```http
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "frontendOpenOrders",
  "user": "******************************************"
}
```

---

### 已成交订单

**Method:** `POST`  
**URL:** `https://api.hyperliquid.xyz/info`  

**Headers:**
```http
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "userFills",
  "user": "******************************************"
}
```

---

### 帐号明细/资金仓位等

**Method:** `POST`  
**URL:** `https://api.hyperliquid.xyz/info`  

**Headers:**
```http
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "clearinghouseState",
  "user": "******************************************"
}
```

---

### 合约明细

**Method:** `POST`  
**URL:** `https://api.hyperliquid.xyz/info`  

**Headers:**
```http
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "meta"
}
```

---

### 交易所明细

**Method:** `POST`  
**URL:** `https://api.hyperliquid.xyz/info`  

**Headers:**
```http
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "metaAndAssetCtxs"
}
```

---

### 时间区间内用户订单历史

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
sec-ch-ua-platform: "macOS"
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) ...
sec-ch-ua: "Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"
Content-Type: application/json
sec-ch-ua-mobile: ?0
Accept: */*
Sec-Fetch-Site: same-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
host: api-ui.hyperliquid.xyz
```

**Request Body:**
```json
{
  "aggregateByTime": true,
  "endTime": 2114380800000,
  "reversed": true,
  "startTime": 1742577300000,
  "type": "userFillsByTime",
  "user": "******************************************"
}
```

---

### 时间区间内金库订单历史

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{
  "aggregateByTime": true,
  "endTime": 2114380800000,
  "reversed": true,
  "startTime": 1742577300000,
  "type": "userFillsByTime",
  "user": "******************************************"
}
```

---

### BTC 15分钟K线 快照

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{
  "req": {
    "coin": "BTC",
    "endTime": *************,
    "interval": "15m",
    "startTime": *************
  },
  "type": "candleSnapshot"
}
```

---

### 子帐号查询

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{
  "type": "subAccounts",
  "user": "******************************************"
}
```

---

### 帐号费率查询

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "userFees", "user": "******************************************" }
```

---

### 现货明细

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "spotMeta" }
```

---

### 代理明细

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "extraAgents", "user": "******************************************" }
```

---

### 法律合同认可明细？

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "legalCheck", "user": "******************************************" }
```

---

### 创建金库

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "legalCheck", "user": "******************************************" }
```

---

### 用户金库锁仓信息

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "userVaultEquities", "user": "******************************************" }
```

---

### 策略员金库明细

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/info`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{ "type": "vaultDetails", "user": "******************************************", "vaultAddress": "******************************************" }
```

---

## Exchange Endpoints

### 金库下单做多

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/exchange`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{
  "action": {
    "grouping": "na",
    "orders": [
      {
        "a": 0,
        "b": true,
        "p": "95455",
        "r": false,
        "s": "0.00307",
        "t": { "limit": { "tif": "FrontendMarket" } }
      }
    ],
    "type": "order"
  },
  "isFrontend": true,
  "nonce": 1742847039351,
  "signature": {
    "r": "0x614abe45c4bdd8eb62f273d51087a80a13153d49e4c2587fcd81d38ec20cd5ba",
    "s": "0x4d22e85d2cd57364b90bf69f8d9a7030fdc50e78cc0454ce4a3da42ab246f998",
    "v": 27
  },
  "vaultAddress": "******************************************"
}
```

---

### 金库订单设置止损

**Method:** `POST`  
**URL:** `https://api-ui.hyperliquid.xyz/exchange`  

**Headers:**
```http
(sec-ch-ua-platform, User-Agent, sec-ch-ua, Content-Type, sec-ch-ua-mobile, Accept,
Sec-Fetch-Site, Sec-Fetch-Mode, Sec-Fetch-Dest, host)
```

**Request Body:**
```json
{
  "action": {
    "grouping": "positionTpsl",
    "orders": [
      {
        "a": 0,
        "b": false,
        "p": "64400",
        "r": true,
        "s": "0",
        "t": { "trigger": { "isMarket": true, "tpsl": "sl", "triggerPx": "70000" } }
      }
    ],
    "type": "order"
  },
  "isFrontend": true,
  "nonce": 1742847551382,
  "signature": {
    "r": "0x38c61ae5eec2275873eaa1c81dec5da63b3716eef5639273392d4caa18e3ebcc",
    "s": "0x5504c5858bf9c7a3893c6e84a87fe53fe0288432e7f3f7616c64272f573316fd",
    "v": 28
  },
  "vaultAddress": "******************************************"
}
```

