# 用户服务 GraphQL

## 接口地址（Endpoint）

```
https://${environment}-api.xbit.live/api/user/graphql
```

- `environment` 表示环境，支持 `unstable`、`staging`

### 1. Mutation：CheckHyperLiquidWallet（检查 HyperLiquid 钱包）

此 mutation 用于检查 HyperLiquid 钱包的状态并获取相关信息。
- **对于 Telegram 用户**：将自动批准代理人、设置推荐码和费用构建器。
- **对于第三方钱包用户**：将返回用于客户端登录的重要信息。

#### 请求示例
```graphql
mutation CheckHyperLiquidWallet {
    checkHyperLiquidWallet {
        approvedAgent
        setReferral
        setFeeBuilder
        agent
        agentName
        feeBuilderAddress
        feeBuilderPercent
        referralCode
    }
}
```

#### 返回字段说明
- **approvedAgent**：是否批准代理人（布尔值）。
- **setReferral**：是否设置推荐码（布尔值）。
- **setFeeBuilder**：是否设置费用构建器（布尔值）。
- **agent**：代理人的地址（字符串）。
- **agentName**：代理人的名称（字符串）。
- **feeBuilderAddress**：费用构建器的地址（字符串）。
- **feeBuilderPercent**：费用构建器设置的最大百分比费用（数字）。
- **referralCode**：系统设定的推荐码（字符串）。

### 3. Mutation：UpdateHyperLiquidWallet（更新 HyperLiquid 钱包）

此 mutation 用于更新第三方钱包用户的 HyperLiquid 钱包状态。在成功签名后调用此 GraphQL，以批准代理人。

#### 请求示例
```graphql
mutation UpdateHyperLiquidWallet {
    updateHyperLiquidWallet(
        input: {
            agentExpiredAt: 1
            setReferral: true
            setFeeBuilder: true
            feeBuilderAddress: "0x12345"
            feeBuilderPercent: 1
            referralCode: "REF_CODE"
        }
    ) {
        approvedAgent
        setReferral
        setFeeBuilder
        agent
        agentName
        feeBuilderAddress
        feeBuilderPercent
        referralCode
    }
}
```

#### 输入字段说明
- **agentExpiredAt**：代理人过期的时间戳（整数）。
- **setReferral**：是否设置推荐码（布尔值）。
- **setFeeBuilder**：是否设置费用构建器（布尔值）。
- **feeBuilderAddress**：费用构建器的地址（字符串）。
- **feeBuilderPercent**：费用构建器设置的最大百分比费用（数字）。
- **referralCode**：为钱包设置的推荐码（字符串）。

#### 返回字段说明
- **approvedAgent**：是否批准代理人（布尔值）。
- **setReferral**：是否设置推荐码（布尔值）。
- **setFeeBuilder**：是否设置费用构建器（布尔值）。
- **agent**：代理人的地址（字符串）。
- **agentName**：代理人的名称（字符串）。
- **feeBuilderAddress**：费用构建器的地址（字符串）。
- **feeBuilderPercent**：费用构建器设置的百分比费用（数字）。
- **referralCode**：为钱包设置的推荐码（字符串）。

## 集成流程图

### Telegram 用户
![集成流程图](../../static/img/technical-design/User_Service_Telgram_User.png)

### 第三方钱包用户
![集成流程图](../../static/img/technical-design/User_service_External_wallet_user.png)

### 4. User sign Hyperliquid create order transaction thru API wallet
  - Endpoint: `https://${environment}-api.xbit.live/api/user/graphql`

```graphql
mutation SignHyperLiquidCreateOrder {
    signHyperLiquidCreateOrder(
        input: {
            action: {
                type: "order"
                orders: [{
                    a: 4
                    b: true
                    p: "1300"
                    s: "0.02"
                    r: false
                    t: {limit: { tif: "Gtc" }} 
                }]
                grouping: "na"
            }
            nonce: 1747394183982
        }
    ) {
        userId
        signature {
            r
            s
            v
        }
    }
}
```

#### GraphQL mutation input
  - `action`: required, object data of order
    - `type`: string, value is "order"
    - `orders`: array, list new order
      - `a`: number, required, id presents to asset
      - `b`: boolean, required, value stands for isBuy
      - `p`: float, required, order price
      - `s`: float, required, order size
      - `r`: boolean, required, reduceOnly
      - `c`: int, optional, Cloid 
      - `t`: object, required, order type. Value should be below: 
      ```
        {
          "limit": {
            "tif": "Alo" | "Ioc" | "Gtc" 
          }
        }
        or 
        {
          "trigger": {
            "isMarket": Boolean,
            "triggerPx": String,
            "tpsl": "tp" | "sl"
          }
        }
      ```
    - `grouping`: string, required, value should be "na" | "normalTpsl" | "positionTpsl"
  - `nonce`: int, required, transaction's nonce

#### Graphql mutation response
  - `user`: string, uuid of user
  - `signature`: object `{r: string, s: string, v: string}`, signature of transaction


### 5. User sign Hyperliquid cancel order transaction thru agent wallet
  - Endpoint: `https://${environment}-api.xbit.live/api/user/graphql`

```graphql
mutation SignHyperLiquidCancelOrder {
    signHyperLiquidCancelOrder(
        input: {
            action: { type: "cancel", cancels: [{ a: 3, o: 30080123762 }] }
            nonce: 1747407577380
        }
    ) {
        userId
        signature {
            r
            s
            v
        }
    }
}
```

#### GraphQL mutation input
  - `action`: required, object data of order
    - `type`: string, value is "cancel"
    - `cancels`: array, list order to cancel
      - `a`: number, required, id presents to asset
      - `o`: number, required, order id
  - `nonce`: int, required, transaction's nonce

#### Graphql mutation response
  - `user`: string, uuid of user
  - `signature`: object `{r: string, s: string, v: string}`, signature of transaction


### 6. User sign Hyperliquid update leverage transaction thru agent wallet
  - Endpoint: `https://${environment}-api.xbit.live/api/user/graphql`

```graphql
mutation SignHyperLiquidUpdateLeverage {
    signHyperLiquidUpdateLeverage(
        input: {
            action: { type: "updateLeverage", asset: 4, isCross: false, leverage: 100 }
            nonce: 1747711813115
        }
    ) {
        userId
        signature {
            r
            s
            v
        }
    }
}
```

#### GraphQL mutation input
  - `action`: required, object data of leverage update
    - `type`: string, value is "updateLeverage"
    - `asset`: number, required, ID representing the asset
    - `isCross`: boolean, required, whether to use cross margin
    - `leverage`: number, required, leverage value to set
  - `nonce`: int, required, transaction's nonce

#### GraphQL mutation response
  - `userId`: string, UUID of the user
  - `signature`: object `{r: string, s: string, v: string}`, signature of the transaction

#### Save user's leverage after submitted successfully hyperliquid
  - Endpoint: `https://${environment}-api.xbit.live/api/graphql-dex`

```graphql
mutation UpdateUserSymbolPreference {
    updateUserSymbolPreference(input: {
      symbol: "BTC",
      leverage: 20,
      isCross: false
    }) {
        isFavorite
        leverage
        isCross
    }
}
```

#### GraphQL mutation input
  - `symbol`: string, required, the trading symbol (e.g., "BTC").
  - `leverage`: number, required, the leverage value to set.
  - `isCross`: boolean, required, whether to use cross margin.

#### GraphQL mutation response
  - `isFavorite`: boolean, whether the symbol is marked as favorite.
  - `leverage`: number, the leverage value set.
  - `isCross`: boolean, whether cross margin is enabled.


# Internal integration
- Protocol: gRPC
- Endpoint: `xbit-user-grpc.${environment}.svc.cluster.local:5001`

## Signing order for user

### Protobuf interface

```proto
syntax = "proto3";

package user.hyperliquid_user.v1;

service HyperLiquidUserService {
  rpc SignUserPlaceOrder(SignUserPlaceOrderRequest) returns (SignUserPlaceOrderResponse) {}
  rpc SignUserCancelOrder(SignUserCancelOrderRequest) returns (SignUserCancelOrderResponse) {}
}

message OrderRequest {
  string type = 1;
  repeated Order orders = 2;
  string grouping = 3;
  Builder builder = 4;

  message Order {
    double a = 1;
    bool b = 2;
    string p = 3;
    string s = 4;
    bool r = 5;
    OrderType t = 6;

    message OrderType {
      oneof type {
        Limit limit = 1;
        Trigger trigger = 2;
      }

      message Limit {
        string tif = 1;
      }

      message Trigger {
        bool isMarket = 1;
        string triggerPx = 2;
        string tpsl = 3;
      }
    }

    string c = 7;
  }

  message Builder {
    string b = 1;
    double f = 2;
  }
}

message SignUserPlaceOrderRequest {
  OrderRequest action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string user_id = 4;
}

message TransactionSignature {
  string r = 1;
  string s = 2;
  int64 v = 3;
}

message SignUserPlaceOrderResponse {
  OrderRequest action = 1;
  int64 nonce = 2;
  string user_id = 3;
  TransactionSignature signature = 4;
  optional string vault_address = 5;
}


message CancelAction {
  string type = 1; // e.g., "cancel"
  repeated Cancel cancels = 2;

  message Cancel {
    double a = 1; // Amount
    int64 o = 2;  // Order ID
  }
}

message SignUserCancelOrderRequest {
  CancelAction action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string user_id = 4;
}

message SignUserCancelOrderResponse {
  CancelAction action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string user_id = 4;
  TransactionSignature signature = 5;
}
```

### Sign user order: method `SignUserPlaceOrder`

- Request:
    - action:  order request parameters like Hyperliquid order api parameter https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/exchange-endpoint#place-an-order
        - `a` is asset
        - `b` is isBuy
        - `p` is price
        - `s` is size
        - `r` is reduceOnly
        - `t` is type
        - `c` is cloid (client order id)
    - `nonce`: bigint, nonce of transaction
    - `vault_address`: string )optional), address of vault
    - `user_id`: string, id of user

```json
{
    "action": {
        "type": "order",
        "orders": [
            {
                "a": 4,
                "b": true,
                "p": "1300",
                "s": "0.02",
                "r": false,
                "t": {
                    "limit": {
                        "tif": "Gtc"
                    }
                }
            }
        ],
        "grouping": "na"
    },
    "nonce": 1746677841212,
    "vault_address": null,
    "user_id": "0c083f23-9d23-4b5f-a1d4-9e8bec90a57f"
}
```

- Response:
    - `signature`: signed signature as `r`, `s`, `v`
```json
{
  "action": {
    "orders": [
      {
        "a": 4,
        "b": true,
        "p": "1300",
        "s": "0.2",
        "r": false,
        "t": {
          "limit": {
            "tif": "Gtc"
          },
          "type": "limit"
        },
        "c": ""
      }
    ],
    "type": "order",
    "grouping": "na",
    "builder": null
  },
  "nonce": "1744947414592",
  "user_id": "01963900-640e-7939-a08f-eead8810860f",
  "signature": {
    "r": "0xdc8f7293704271a15d4a5e5eb14dca22ef1a52e55eaa2d9cad602626d4df4d6e",
    "s": "0x6f50f3b8f2241cbc5372fb06126aa8411e7822983e996520694716f5bbc36176",
    "v": "27"
  },
  "vault_address": null,
  "_vault_address": "vault_address"
}
```

### Sign Cancel user order: method `SignUserCancelOrder`

    - Request
        - `action`: cancel order parameter
            - `cancels`: list cancel order
                - `a`: number, asset id
                - `o`: number, order id
        - `nonce`: bigint, nonce of transaction
        - `vault_address`: string )optional), address of vault
        - `user_id`: string, id of user

```json
{
  "action": {
    "type": "cancel",
    "cancels": [
      {
        "a": 4,
        "o": 28381275363
      }
    ]
  },
  "nonce": 1744947414592,
  "vault_address": null,
  "user_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4"
}
```

- Response:
    - `signature`: signed signature as `r`, `s`, `v`
```json
    {
    "action": {
        "type": "cancel",
        "cancels": [
            {
                "a": 4,
                "o": 28381275363
            }
        ]
    },
      "signature": {
        "r": "0xdc8f7293704271a15d4a5e5eb14dca22ef1a52e55eaa2d9cad602626d4df4d6e",
        "s": "0x6f50f3b8f2241cbc5372fb06126aa8411e7822983e996520694716f5bbc36176",
        "v": "27"
    },
    "nonce": 1744947414592,
    "vault_address": null,
    "user_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4"
}
```


## Signing order for vault owner

### Protobuf interface

```proto
syntax = "proto3";

package user.hyperliquid_vault.v1;

import "google/protobuf/empty.proto";

service HyperLiquidVaultService {
  rpc CreateMasterWallet(google.protobuf.Empty) returns (MasterWalletResponse) {}
  rpc CreateMasterWalletAgent(CreateMasterWalletAgentRequest) returns (CreateMasterWalletAgentResponse) {}
  rpc SignPlaceOrder(SignPlaceOrderRequest) returns (SignPlaceOrderResponse) {}
  rpc ImportMasterWallet(ImportMasterWalletRequest) returns (MasterWalletResponse) {}
  rpc SignCancelOrder(SignCancelOrderRequest) returns (SignCancelOrderResponse) {}
}

message MasterWalletResponse {
  string id = 1;
  string address = 2;
}

message CreateMasterWalletAgentRequest {
  string master_wallet_id = 1;
}

message CreateMasterWalletAgentResponse {
  string id = 1;
  string address = 2;
  string master_wallet_id = 3;
}

message OrderRequest {
  string type = 1;
  repeated Order orders = 2;
  string grouping = 3;
  Builder builder = 4;

  message Order {
    double a = 1;
    bool b = 2;
    string p = 3;
    string s = 4;
    bool r = 5;
    OrderType t = 6;

    message OrderType {
      oneof type {
        Limit limit = 1;
        Trigger trigger = 2;
      }

      message Limit {
        string tif = 1;
      }

      message Trigger {
        bool isMarket = 1;
        string triggerPx = 2;
        string tpsl = 3;
      }
    }

    string c = 7;
  }

  message Builder {
    string b = 1;
    double f = 2;
  }
}

message SignPlaceOrderRequest {
  OrderRequest action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string master_wallet_id = 4;
}

message TransactionSignature {
  string r = 1;
  string s = 2;
  int64 v = 3;
}

message SignPlaceOrderResponse {
  OrderRequest action = 1;
  int64 nonce = 2;
  string master_wallet_id = 3;
  TransactionSignature signature = 4;
  optional string vault_address = 5;
}

message ImportMasterWalletRequest {
  string private_key = 1;
}

message CancelAction {
  string type = 1; // e.g., "cancel"
  repeated Cancel cancels = 2;

  message Cancel {
    double a = 1; // Amount
    int64 o = 2;  // Order ID
  }
}

message SignCancelOrderRequest {
  CancelAction action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string master_wallet_id = 4;
}

message SignCancelOrderResponse {
  CancelAction action = 1;
  int64 nonce = 2;
  optional string vault_address = 3;
  string master_wallet_id = 4;
  TransactionSignature signature = 5;
}
```

### gRPC Method: `CreateMasterWallet`

This method is used to create a new master wallet as EOA wallet for the vault owner.

- Request
```json
{}
```

- Response
    - id: uuid, id of master wallet
    - address:  string, address of master wallet
```json
{
    "id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4",
    "address": "0xabcdef1234567890"
}
```

### gRPC Method: `ImportMasterWallet`
- System will encrypt input private key of master wallet and save encrypted data.

- Request
    - `private_key`: string, private key of master wallet
```json
{
    "private_key": "0xqwertyuiqweqwiajskdaasdahksdahdkhqiweu"
}
```

- Response
    - `id`: string, the ID of the created agent wallet.
    - `address`: string, the address of the created agent wallet.
    - `master_wallet_id`: string, the ID of the associated master wallet.
```json
{
    "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
    "address": "******************************************",
}
```

### gRPC Method: `CreateMasterWalletAgent`
- This method is used to create an API wallet for a specific master wallet.

- Request
    - `master_wallet_id`: string, the ID of the master wallet for which the agent wallet is being created.
```json
{
    "master_wallet_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4"
}
```

- Response
    - `id`: string, the ID of the created agent wallet.
    - `address`: string, the address of the created agent wallet.
    - `master_wallet_id`: string, the ID of the associated master wallet.
```json
{
    "id": "a1b2c3d4-5678-90ab-cdef-1234567890ab",
    "address": "******************************************",
    "master_wallet_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4"
}
```

### gRPC Method: `SignPlaceOrder`

This method is used to sign a place order request for a vault owner (use created API wallet for signing).

- Request
    - `action`: order request parameters.
    - `nonce`: bigint, nonce of the transaction.
    - `vault_address`: string (optional), address of the vault.
    - `master_wallet_id`: string, ID of the master wallet.
```json
{
    "action": {
        "type": "order",
        "orders": [
            {
                "a": 4,
                "b": true,
                "p": "1300",
                "s": "0.02",
                "r": false,
                "t": {
                    "limit": {
                        "tif": "Gtc"
                    }
                }
            }
        ],
        "grouping": "na"
    },
    "nonce": 1746677841212,
    "vault_address": "0xasdas",
    "master_wallet_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4"
}
```

- Response
    - `signature`: signed signature as `r`, `s`, `v`.
```json
{
    "action": {
        "orders": [
            {
                "a": 4,
                "b": true,
                "p": "1300",
                "s": "0.02",
                "r": false,
                "t": {
                    "limit": {
                        "tif": "Gtc"
                    }
                },
                "c": ""
            }
        ],
        "type": "order",
        "grouping": "na",
        "builder": null
    },
    "nonce": 1746677841212,
    "master_wallet_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4",
    "signature": {
        "r": "0xdc8f7293704271a15d4a5e5eb14dca22ef1a52e55eaa2d9cad602626d4df4d6e",
        "s": "0x6f50f3b8f2241cbc5372fb06126aa8411e7822983e996520694716f5bbc36176",
        "v": 27
    },
    "vault_address": null
}
```

### gRPC Method: `SignCancelOrder`

This method is used to sign a cancel order request for a vault owner (use created API wallet for signing).

- Request
    - `action`: cancel order parameters.
    - `nonce`: bigint, nonce of the transaction.
    - `vault_address`: string (optional), address of the vault.
    - `master_wallet_id`: string, ID of the master wallet.
```json
{
    "action": {
        "type": "cancel",
        "cancels": [
            {
                "a": 4,
                "o": 28381275363
            }
        ]
    },
    "nonce": 1746677841212,
    "vault_address": "0xasdas",
    "master_wallet_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4"
}
```

- Response
    - `signature`: signed signature as `r`, `s`, `v`.
```json
{
    "action": {
        "type": "cancel",
        "cancels": [
            {
                "a": 4,
                "o": 28381275363
            }
        ]
    },
    "nonce": 1746677841212,
    "master_wallet_id": "b6edb3ce-5ec3-4f85-8953-3701549c03f4",
    "signature": {
        "r": "0xdc8f7293704271a15d4a5e5eb14dca22ef1a52e55eaa2d9cad602626d4df4d6e",
        "s": "0x6f50f3b8f2241cbc5372fb06126aa8411e7822983e996520694716f5bbc36176",
        "v": 27
    },
    "vault_address": "0xasdas",
}
```