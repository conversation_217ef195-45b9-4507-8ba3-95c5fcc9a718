# 2. 架构简介

### 总体架构
为了满足不同类型用户的需求，平台提供 **3 种登录方式**：
1. **Telegram 登录**：自动生成 6 条链的钱包，方便快速上手。
2. **第三方钱包连接**：支持 BossWallet、OKXWallet、MetaMask 等。
3. **WalletConnect**：统一标准接口，方便更多钱包适配。

---

### 2.1 总体架构

平台采用分布式、多模块的架构设计，将 meme 币交易和衍生品交易融合于同一生态体系，主要包括以下层次：

1. **前端展示层（Flutter / React）**  
   - 提供用户友好的操作界面，多语言支持（中英文切换）。
   - 用户钱包连接（Telegram / 第三方钱包 / WalletConnect）。
   - 负责交易流程引导、订单输入（包括市价、限价、杠杆、多空、止盈止损、网格等）、风险提示等。
   - 第一阶段对接 HyperLiquid API/SDK，直接在前端进行下单操作。

2. **后端业务层（Golang）**  
   - 主要负责平台基础服务，如用户身份认证、订单数据校验、订单记录管理等。
   - 提供跨链或兑换接口调用逻辑（如与 Rango、Thorchain 的集成）。
   - 第二阶段开始，后端将实现订单转发接口，对接多家衍生品 DEX 或流动性池，并追踪订单状态。

3. **Rust 模块（HyperTrader / 量化信号处理）**  
   - 处理高频或策略化交易场景，与 HyperLiquid SDK 深度对接。
   - 接收量化团队（或交易员）的信号策略，管理金库（vault）及资金调度。  
     - 若交易员选择“实盘交易”，可创建 vault 并将资金托管于此；私钥管理需要进一步商讨安全策略（例如是否由平台代码持有密钥，或采用 MPC/多签方案）。
   - 生成信号指标给前端（用户可一键下单），或自动化执行量化策略下单。

4. **跨链聚合器 / 流动性对接**  
   - 接入 Rango、Thorchain 等第三方服务，实现跨链资产兑换。
   - 提供用户从多链资产切换到 ARB-USDC 的通道，以便在 HyperLiquid 进行交易。
   - 后续可扩展对接更多跨链聚合器或流动性池。

---

### 2.2 关键架构特点

1. **meme 币与衍生品交易结合**  
   - 用户可通过相同账号/钱包体系，既进行 meme 币交易，也可进行衍生品合约交易；前端对接同一登录流程，提升用户体验。

2. **第一阶段基于 HyperLiquid SDK**  
   - 由于 HyperLiquid 目前仅支持 ARB 链上的 USDC，用户需要先完成跨链或兑换操作，将资产转换为 ARB-USDC 后再进行合约交易。

3. **三种交易方式**  
   - **传统CEX页面下单**：前端直接下单，支持做多/做空、市价/限价、止盈/止损、TWAP、网格等策略，主要面向普通用户。  
   - **Rust信号指标**：Rust 模块提供量化信号，前端展示后，用户可在页面一键跟单；主要面向有交易提示需求的用户。  
   - **投资组合**：交易员或量化脚本通过 HyperTrader API 下单，实盘交易时资金托管在 vault 内；面向专业交易员和量化团队。

4. **跨链与兑换**  
   - 利用 Rango、Thorchain 等跨链服务，为用户提供多链资产到 ARB-USDC 的快速兑换路径。
   - 后续可扩展更多桥和聚合器，提升交易灵活度。

5. **订单转发与多流动性池**（第二阶段）  
   - 现阶段（第一阶段）订单直接对接 HyperLiquid；  
   - 第二阶段在后端实现“订单转发接口”，将用户订单根据策略或市场深度分配到多个衍生品 DEX 或流动性池。

6. **安全与密钥管理**  
   - 私钥管理方案需结合业务场景与安全要求，可能考虑 MPC、多签或托管等方式。  
   - 对量化策略的资金 vault，需进行额外权限控制和风控。

---