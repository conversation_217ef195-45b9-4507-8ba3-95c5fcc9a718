# 6. 参考资料

### 通用

- **DEX 月交易量统计**  
  [https://www.theblock.co/data/decentralized-finance/dex-non-custodial/dex-volume-monthly](https://www.theblock.co/data/decentralized-finance/dex-non-custodial/dex-volume-monthly)

- **DEX 24H 交易量统计**  
  [https://www.coingecko.com/en/exchanges/decentralized](https://www.coingecko.com/en/exchanges/decentralized)

- **Radium 去中心化交易所 (SOL/Orderly Network)**  
  [https://docs.raydium.io/raydium/traders/raydium-perps/trading-basics](https://docs.raydium.io/raydium/traders/raydium-perps/trading-basics)

- **Orderly Network SDK (支持 11 链, 102 币种)**  
  [https://orderly.network/docs/introduction/trade-on-orderly/supported-chains](https://orderly.network/docs/introduction/trade-on-orderly/supported-chains)

- **Orderly Demo**  
  [https://orderlynetwork.github.io/example-dex/](https://orderlynetwork.github.io/example-dex/)

- **Orderly 资管合约 (NEAR)**  
  [https://nearblocks.io/address/asset-manager.orderly-network.near?tab=contract](https://nearblocks.io/address/asset-manager.orderly-network.near?tab=contract)

- **Orderly 审计报告**  
  [https://github.com/OrderlyNetwork/Audits/tree/main](https://github.com/OrderlyNetwork/Audits/tree/main)

- **Chainlink 自动化合约 (用于触发限价交易等)**  
  [https://chain.link/automation](https://chain.link/automation)

- **Chainlink 价格预言机 (支持所有主流链)**  
  [https://data.chain.link/](https://data.chain.link/)

- **Chainlink 驱动去中心化治理**  
  [https://blog.chain.link/how-chainlink-powers-decentralized-governance-zh/](https://blog.chain.link/how-chainlink-powers-decentralized-governance-zh/)

- **Chainlink 及 AMM**  
  [https://chain.link/education-hub/what-is-an-automated-market-maker-amm](https://chain.link/education-hub/what-is-an-automated-market-maker-amm)

- **CometBFT 自由链 (dydx 自有链基于这个)**  
  [https://medium.com/the-interchain-foundation/what-is-cometbft-interchain-stack-highlights-4bcfccc17f7b](https://medium.com/the-interchain-foundation/what-is-cometbft-interchain-stack-highlights-4bcfccc17f7b)

- **dydx 和 hyperliquid 吞吐量**  
  [https://www.bitget.com/news/detail/12560604140995](https://www.bitget.com/news/detail/12560604140995)

- **区块链吞吐量数据**  
  [https://chainspect.app/dashboard](https://chainspect.app/dashboard)

- **DEX 交易量**  
  [https://app.artemisanalytics.com/sectors](https://app.artemisanalytics.com/sectors)

- **dydx TVL**  
  [https://community.chaoslabs.xyz/dydx-v4/risk/overview](https://community.chaoslabs.xyz/dydx-v4/risk/overview)

- **dydx 节点及效率看板**  
  [https://observatory.zone/dydx](https://observatory.zone/dydx)

- **永续合约 DEX 的进化论**  
  [https://www.bitget.com/news/detail/12560604140995](https://www.bitget.com/news/detail/12560604140995)

- **DEX 高频交易论文**  
  [https://arxiv.org/pdf/2009.14021](https://arxiv.org/pdf/2009.14021)

- **DYDX v4 js客户端**  
  [https://github.com/dydxprotocol/v4-clients/tree/main/v4-client-js](https://github.com/dydxprotocol/v4-clients/tree/main/v4-client-js)

- **APEX Omni**  
  [https://omni.apex.exchange/userApiCenter](https://omni.apex.exchange/userApiCenter)

- **Molaris 智能合约数据**  
  [https://developers.moralis.com/chains/base/](https://developers.moralis.com/chains/base/)

- **Cosmos 链上数据**  
  [https://www.mintscan.io/cosmos](https://www.mintscan.io/cosmos)

- **CometBFT**  
  [https://docs.cometbft.com/v1.0/explanation/core/using-cometbft](https://docs.cometbft.com/v1.0/explanation/core/using-cometbft)

- **HyperLiquid 交易 API**  
  [https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/exchange-endpoint](https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/exchange-endpoint)

---

### Perps DEX (derivatives)

- [https://bluefin.io/](https://bluefin.io/)
- [https://app.hyperliquid.xyz/trade](https://app.hyperliquid.xyz/trade)
- [https://dydx.trade/](https://dydx.trade/)
- [https://v4.testnet.dydx.exchange](https://v4.testnet.dydx.exchange)
- [https://orderly.network/](https://orderly.network/)
- [https://pro.woofi.com/ (orderly)](https://pro.woofi.com/)
- [https://pro.fusionx.finance/ (orderly)](https://pro.fusionx.finance/)
- [https://app.vooi.io/](https://app.vooi.io/)
- [https://www.sei.io/explore](https://www.sei.io/explore)
- [https://github.com/hyperliquid-dex/hyperliquid-python-sdk](https://github.com/hyperliquid-dex/hyperliquid-python-sdk)

---

### Chain Abstraction

- [https://docs.vooi.io/vooi-roadmap](https://docs.vooi.io/vooi-roadmap)

---

### Oracle

- [https://www.pyth.network/](https://www.pyth.network/)

---

### Cross-chain Messaging

- [https://layerzero.network/](https://layerzero.network/)
- [https://www.axelar.network/](https://www.axelar.network/)

---

### Bridge Aggregator

- [https://rango.exchange/](https://rango.exchange/)
- [https://app.symbiosis.finance/swap](https://app.symbiosis.finance/swap)
- [https://thorchain.org/integrate](https://thorchain.org/integrate)
- [https://li.fi/](https://li.fi/)
- [https://ff.io](https://ff.io)

---

### Open-source Decentralised Protocol

- [https://vega.xyz/](https://vega.xyz/)

---

### Perps Ranking

- [https://rankfi.com/perpetual-dexs/](https://rankfi.com/perpetual-dexs/)

---

### Statistics

- [https://dune.com/vooi/vooidash](https://dune.com/vooi/vooidash)
- [https://dune.com/uwusanauwu/perps](https://dune.com/uwusanauwu/perps)
- [https://dune.com/x3research/hyperliquid](https://dune.com/x3research/hyperliquid)
- [https://defillama.com/trending-contracts](https://defillama.com/trending-contracts)
- [https://dune.com/orderly_network/orderly-dashboard](https://dune.com/orderly_network/orderly-dashboard)
- [https://app.artemisanalytics.com/sectors](https://app.artemisanalytics.com/sectors)
- [https://purrsec.com/address/0x6a7139543bff9c60a9c9edde60b65a2c36c7174a/perp-portfolio](https://purrsec.com/address/0x6a7139543bff9c60a9c9edde60b65a2c36c7174a/perp-portfolio)
- [https://parsec.fi/landing/api](https://parsec.fi/landing/api)

---

### Futures Market Proxy CA

- [https://docs.kwenta.io/developers/deployed-contracts/v2-futures-market-proxy-contracts](https://docs.kwenta.io/developers/deployed-contracts/v2-futures-market-proxy-contracts)
- [https://orderly.network/docs/build-on-omnichain/addresses](https://orderly.network/docs/build-on-omnichain/addresses)

---