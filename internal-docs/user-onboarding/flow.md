# 2. 交易流程概述

### 2.1 注册 & 登录

1. 用户通过第三方用户管理系统注册账号。
2. 注册及登录方法有邮箱OTP，谷歌社交媒体OAuth，以及第三方钱包。
3. 系统将生成内置钱包（目前 Solana，Ethereum, Tron）

### 2.2 资产准备（跨链 / 充值 / 兑换）

#### 第一版本（通过 Rango）

1. 用户可将Solana, BNB,Ethereum, Tron 链上的资产跨链至 ARB 链。
2. 用户可将Solana, BNB,Ethereum, Tron 链上的资产兑换成 USDC。

#### 延伸版本 （通过 HyperLiquid 现货 / 未开发）

1. 通过 HyperUnit 生成用户专属的 Solana 入金地址 (参考 [HyperUnit API 文档](https://docs.hyperunit.xyz/developers/api/generate-address))
```curl -X GET https://api.hyperunit.xyz/gen/solana/hyperliquid/sol/<内置钱包默认 ARB HyperLiquid 地址>```
2. 从内置 Solana 钱包将 Solana 转入 HyperUnit 生成的地址
3. 调用 HyperLiquid API 把 Solana 卖掉
4. 将 HyperLiquid Spot 账户余额 USDC 转到 HyperLiquid Perps (参考 [HyperLiquid API 文档](https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/api/exchange-endpoint#transfer-from-spot-account-to-perp-account-and-vice-versa))

### 2.3 交易模式

1. **传统 CEX 形式**  
   - 用户在前端界面选择做多/做空、市价/限价、杠杆倍数等，发起交易请求，前端直接调用 HyperLiquid API 下单。  
   - 若要执行网格、TWAP 等高级下单策略，前端可做相应封装。（版本 1.0.0. 未开发）

2. <s>**Rust 信号指标**</s>
   - <s>Rust 模块接收量化策略或行情信号，计算并将“买/卖”建议发送给前端；用户可一键执行下单。</s>

3. <s>**投资组合（量化 / Vault）**</s>
   - <s>交易员在 Rust HyperTrader 中配置策略，并向用户提供“策略跟单”或“资金托管”服务。</s>
   - <s>若为实盘交易，资金托管在 vault 中；Rust 模块根据交易员指令自动执行买卖操作。</s>

---
