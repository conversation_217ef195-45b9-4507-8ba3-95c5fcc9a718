# 4. Roadmap

下表概述项目阶段性规划，帮助团队把握重点并稳步迭代：

| 阶段              | 目标与功能点                                                                                         | 关键任务                                                         | 预期完成时间        |
| :---------------- | :--------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------- | :------------------ |
| **阶段 1**        | - 整合 meme 交易与衍生品合约交易<br/>- 基于 HyperLiquid 进行合约交易<br/>- 跨链兑换 ARB-USDC          | - 实现前端直接下单（市价/限价/做多/做空等）<br/>- 对接 Rango、Thorchain<br/>- 三种登录方式（**安全优先**） <br/>- Vault 管理方案落地| 近期（2~3 个月）    |
| **阶段 2**        | - 实现订单转发接口<br/>- 对接多家衍生品 DEX 或流动性池<br/>- 强化量化策略与 Rust HyperTrader            | - 后端实现多家 DEX 的订单路由<br/>- 优化量化策略信号统计与自动化 | 中期（4~6 个月）    |
| **阶段 3**        | - 主链研发<br/>- 引入订单撮合功能<br/>- 多链拓展（支持更多桥与更多资产）<br/>- 优化安全与合规                           | - 自研或整合撮合模块<br/>- 扩展跨链聚合器与更多链支持<br/>- 完善监管合规 | 远期（6～12 个月+）   |

---