# 1. XBIT 概述

XBIT 交易所为永续合约交易所与 MEME 币交易所深度融合，提供一站式服务，支持一级市场 MEME 币交易、跨链现货交易及衍生品合约交易。
在第一阶段，平台基于 HyperLiquid 的 API 进行交易，支持 ARB 链上的 USDC；
同时，若用户账户内无 ARB-USDC，可通过对接第三方跨链聚合器（Rango）完成跨链或兑换，保证交易顺畅。

**阶段一暂不撮合用户的订单，阶段二系统需实现转发用户的订单到各池子成交。**

该内部技术文档将专注于去中心化合约交易所。关于 MEME 币交易相关请参考 [《XBIT去中心化交易所-需求文档》](https://kdocs.cn/l/chAZ6QIPzYgB)。<br/>
本文档重点介绍去中心化合约交易所部分的整体设计、关键技术实现、订单转发机制、风险控制和未来扩展方向。

### 永续合约交易所模块

<details>
  <summary>**Go Backend XBIT** - 基于 Go 开发的后端，全力支撑 XBIT 去中心化永续合约交易所的功能。</summary>
</details>

<details>
  <summary>**XBIT User Service** - 基于 NestJS, TypeScript 开发的后端微服务，提供用户认证、钱包管理和交易签名服务</summary>
- 项目架构
  - 微服务架构 (Microservices Architecture): 使用 NestJS Monorepo 结构
  - 多应用模式: 个别独立的模块化应用服务
  - public-graphql: 面向用户的 GraphQL API 服务 (端口 3000)
  - internal-grpc: 内部 gRPC 服务，用于密钥管理和交易签名 (端口 5001)
  - commander: 命令行工具应用
- 核心技术框架
  - 后端框架: NestJS (Node.js 企业级框架)
  - 编程语言: TypeScript
  - API 层:
    - GraphQL (Apollo Server) - 用户端 API
    - gRPC - 内部服务通信
  - 数据库:
    - PostgreSQL (主数据库，使用 MikroORM)
    - MongoDB (使用 Mongoose)
    - Redis (缓存和会话管理)
    - 消息队列: NATS, MQTT
    - 认证: JWT, Passport (支持多种认证方式)
  - 区块链和钱包集成
    - 钱包服务: Turnkey (企业级钱包基础设施)
  - 区块链支持:
    - Ethereum (ethers.js)
    - Solana (@solana/web3.js)
    - Tron (tronweb)
  - 加密库: tweetnacl, ethereum-cryptography
</details>

<details>
  <summary>**XBIT Web** - 基于 React, Typescript 开发的前端，对接 HyperLiquid API 进行交易。</summary>
- 前端框架 & 构建工具
  - React - JavaScript React 框架
  - TypeScript - 强类型 JavaScript 超集
  - Vite - 现代化构建工具和开发服务器
  - React Router DOM - 客户端路由管理
- 样式 & UI 框架
  - Tailwind CSS - 原子化 CSS 框架
  - Radix UI - 无障碍的 UI 组件库
  - Dialog, Dropdown, Select, Tabs, Tooltip 等组件
  - Framer Motion - 动画库
  - Styled Components - CSS-in-JS 样式方案
  - Lucide React - 图标库
- 状态管理
  - Redux Toolkit - 现代化 Redux 状态管理
  - React Redux - React-Redux 绑定
  - Redux Persist - 状态持久化
- 数据获取 & API
  - Apollo Client - GraphQL 客户端
  - GraphQL - 查询语言和运行时
  - GraphQL Code Generator - 自动生成 TypeScript 类型
  - TanStack React Query - 服务器状态管理
  - Axios - HTTP 客户端
- 区块链 & 钱包集成
  - 多链钱包支持
    - Solana 生态系统
      - @solana/web3.js - Solana JavaScript SDK
      - @solana/wallet-adapter-* - 钱包适配器
      - Phantom, Solflare, Trust Wallet 支持
    - 以太坊生态系统
      - Wagmi - React Hooks for Ethereum
      - Viem - TypeScript 以太坊接口
      - RainbowKit - 钱包连接 UI
      - Ethers - 以太坊库
- 交易 & DeFi 集成
  - HyperLiquid API - @nktkas/hyperliquid 期货交易
  - Rango SDK - 跨链桥接服务
  - Cross-chain Bridge - 多链资产转移
- 图表 & 数据可视化
  - 交易图表
    - ApexCharts + React ApexCharts - 专业交易图表
    - Recharts - React 图表库
    - TradingView Datafeeds - 自定义数据源集成
  - 数据处理
    - BigNumber.js - 高精度数值计算
    - Decimal.js - 精确小数运算
    - Day.js - 轻量级日期处理
- 实时通信 & 订阅
  - 实时数据
    - MQTT - 消息队列遥测传输
    - GraphQL WebSocket - 实时 GraphQL 订阅
    - WebSocket - 原生 WebSocket 连接
  - 推送通知
    - Firebase - 推送通知和分析
    - Sentry - 错误监控和性能追踪
- 代码质量
  - ESLint - 代码检查
  - Prettier - 代码格式化
  - TypeScript ESLint - TypeScript 专用规则
  - Husky - Git hooks
</details>

<details>
  <summary>**XBIT Mobile App** - 基于 Flutter 开发的移动应用，对接区块链钱包和交易功能。</summary>
- 核心技术框架
  - Flutter - 跨平台移动应用开发框架
  - Dart - 编程语言
  - Material Design - UI设计系统
- 架构模式
  - Clean Architecture - 分层架构（Data/Domain/Presentation）
  - BLoC Pattern - 状态管理模式
  - Dependency Injection - 使用 GetIt 进行依赖注入
  - Modular Architecture - 基于 Feature 的模块化架构
- 核心依赖库
  - 状态管理
    - flutter_bloc - BLoC状态管理
    - bloc - BLoC核心库
  - 网络通信
    - dio - HTTP客户端
    - retrofit - REST API客户端生成器
    - graphql - GraphQL客户端
    - mqtt_client - MQTT消息队列客户端
  - 路由导航
    - go_router - 声明式路由管理
  - 本地存储
    - flutter_secure_storage - 安全存储
    - hive & hive_flutter - 本地数据库
    - shared_preferences: 2.5.3 - 简单键值存储
  - Firebase集成
    - firebase_core - Firebase核心
    - firebase_analytics - 分析统计
    - firebase_crashlytics - 崩溃报告
    - firebase_messaging - 推送通知
    - firebase_remote_config - 远程配置
  - UI组件
    - flutter_svg - SVG图像支持
    - cached_network_image - 网络图片缓存
    - fl_chart - 图表组件
    - google_fonts - Google字体
    - auto_size_text - 自适应文本
  - 钱包连接
    - webview_flutter - WebView支持
    - url_launcher - URL启动器
    - 自定义 wallet_connect 包 - 钱包连接功能
- 支持的区块链网络
  - Ethereum (Chain ID: 1)
  - BSC (Chain ID: 56)
  - Base (Chain ID: 8453)
  - Solana (Chain ID: 1399811149)
  - Tron (Chain ID: 728126428)
- 支持的钱包（将更新为 Turnkey）
  - Boss Wallet, OKX, MetaMask, Trust Wallet, Token Pocket, Bitget
  - imToken, Zerion, Rainbow, TronLink, Ledger Live, Phantom
- 开发工具
  - 代码生成: build_runner, json_serializable, freezed
  - 测试: flutter_test, mocktail, bloc_test
  - 代码质量: very_good_analysis
  - GraphQL代码生成: graphql_codegen
- 平台支持
  - iOS, Android, Web, Windows
</details>

<details>
  <summary>【**DEPRECATED**】</summary>
- <s>**HyperLiquid Rust SDK** - 团队内部维护版本 Rust SDK，提高安全性的同时解决 SDK 本身的不完善。</s>
- <s>**HyperTrader** - 基于 Rust 开发的交易模块，专用于提供接口让交易员/量化机器人对接。</s>
  - <s>**信号指标** - 为前端提供指标，供用户一键下单</s>
  - <s>**HyperTrader API** - 开放给交易员/量化机器人对接 </s>
  - <s>**金库订单管理** - 通过交易员给出的信号及仓位信息，对接 HyperLiquid 进行仓位/订单管理</s>
  - <s>**胜率/数据统计** - 为所有策略组以及信号进行胜率统计及管理</s>
</details>

