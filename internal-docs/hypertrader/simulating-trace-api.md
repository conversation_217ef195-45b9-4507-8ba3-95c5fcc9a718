# 模拟盘交易API

## 发起限价单(开仓)

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_create_limit_order

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| symbol| string | 币种 |
| order_type| string| "open"（开仓），"close"（关仓）|
| quantity| f64| 数量 |
| order_action_type|string|  “long”(做多)，"short"(做空)|
| limit_price|f64| 限价价格|
| mode| string| "cross"(全仓)， "isolate"(逐仓)|

### 可选项
|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| leverage| i32| 杠杆|
| take_profit_price| string| 止盈价|
| stop_loss_price| string| 止损价|

### 示例：
```json
{
    "user_id": 2,
    "symbol": "ETH",
    "order_type": "open",
    "quantity": 1.0,
    "order_action_type": "long",
    "limit_price": 1800,
    "mode": "isolate"
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": {
        "order_id": 85
    }
}
```

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| order_id: |i32| 订单ID，用于后续撤销订单时使用 |

## 发起限价单(平仓/减仓)

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_create_limit_order

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| symbol| string | 币种 |
| order_type| string| "open"（开仓），"close"（关仓）|
| limit_price|f64| 限价价格|

### 可选项
|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| quantity| f64| 减仓的数量，如果不填就是平仓 |

### 示例：
```json
{
    "user_id": 2,
    "symbol": "ETH",
    "order_type": "close",
    "quantity": 1.0,
    "limit_price": 1800
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": {
        "order_id": 85
    }
}
```

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| order_id: |i32| 订单ID，用于后续撤销订单时使用 |


## 发起市价单(开仓)

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_create_market_order

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| symbol| string | 币种 |
| order_type| string| "open"（开仓），"close"（关仓）|
| quantity| f64| 数量 |
| order_action_type|string|  “long”(做多)，"short"(做空)|
| mode| string| "cross"(全仓)， "isolate"(逐仓)|

### 可选项
|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| leverage| i32| 杠杆|
| take_profit_price| string| 止盈价|
| stop_loss_price| string| 止损价|

### 示例：
```json
{
    "user_id": 2,
    "symbol": "ETH",
    "order_type": "open",
    "quantity": 1.0,
    "order_action_type": "long",
    "mode": "isolate"
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": ""
}
```

## 发起市价单(平仓/减仓)

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_create_market_order

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| symbol| string | 币种 |
| order_type| string| "open"（开仓），"close"（关仓）|

### 可选项
|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| quantity| f64| 减仓的数量，如果不填就是平仓 |

### 示例：
```json
{
    "user_id": 2,
    "symbol": "ETH",
    "order_type": "close",
    "quantity": 1.0,
    "limit_price": 1800
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": {
        "order_id": 85
    }
}
```

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| order_id: |i32| 订单ID，用于后续撤销订单时使用 |


## 撤销订单

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_cancel_order

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| order_id: |i32| 订单ID |

### 示例：
```json
{
    "order_id": 77
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": ""
}
```


## 修改杠杆

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_update_leverage

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| symbol| string | 币种 |
| leverage| i32| 杠杆|

### 示例：
```json
{
    "user_id": 2,
    "symbol": "ETH",
    "leverage": 20,
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": ""
}
```


## 获取订单列表

**GET** http://API_HOST:5800/apis/v0/dev/cs/get_active_orders

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |

### 示例：
```json
{
    "user_id": 2,
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": {
        "84": {
            "order_id": 84,
            "user_id": 2,
            "symbol": "BTC",
            "order_type": "open",
            "mode": "cross",
            "order_action_type": "long",
            "is_active": true,
            "price": 2460.0,
            "size": 10.0,
            "created_date": null,
            "tp_price": 2500.0,
            "leverage": 10,
            "strategy_id": null
        },
        "85": {
            "order_id": 85,
            "user_id": 2,
            "symbol": "BTC",
            "order_type": "open",
            "mode": "cross",
            "order_action_type": "long",
            "is_active": true,
            "price": 2460.0,
            "size": 10.0,
            "created_date": null,
            "leverage": 10,
            "strategy_id": null
        }
    }
}
```


## 获取仓位列表

**GET** http://API_HOST:5800/apis/v0/dev/cs/get_active_position

### body

### 必填项：

空

### 返回：
```json
{
    "code": 200800000,
    "msg": {
        "80": {
            "basic": {
                "position_id": 80,
                "user_id": 2,
                "mode": "cross",
                "symbol": "ETH",
                "size": 0.1,
                "is_open": true,
                "entry_price": 2481.05,
                "tp_price": null,
                "sp_price": null,
                "direction": "long",
                "leverage": 10,
                "updated_date": "2025-05-17T07:47:55.822397Z"
            },
            "margin_required": 24.045500000000004,
            "maintenance_margin_required": 4.809100000000001,
            "liq_price": -3170506.180561225,
            "unrealized_pnl": -7.65
        }
    }
}
```

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| position_id | i32 | 仓位ID |
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| symbol| string | 币种 |
| size| f64| 数量 |
| direction |string|  “long”(做多)，"short"(做空)|
| mode| string| "cross"(全仓)， "isolate"(逐仓)|
| leverage| i32| 杠杆|
| entry_price| f64 | 买入价格 |
| tp_price| string| 止盈价|
| sp_price| string| 止损价|
| margin_required | f64 | 所需保证金 |
| maintenance_margin_required | f64 | 维持保证金 |
| liq_price | f64 | 强平价格 |
| unrealized_pnl| f64 | 未实现盈亏|



## 获取用户（金库）列表

**GET** http://API_HOST:5800/apis/v0/dev/cs/get_all_users

### body

### 必填项：

空

### 返回：
```json
{
    "code": 200800000,
    "msg": [
        {
            "basic": {
                "id": 2,
                "username": "test",
                "email": "onnoe",
                "fund": 310950.06069500005,
                "created_at": "2025-04-22T10:30:57.411120Z"
            },
            "avail_value": 310946.22547625005,
            "unrealized_pnl": -7.605
        },
        {
            "basic": {
                "id": 4,
                "username": "test04",
                "email": "<EMAIL>",
                "fund": 50115.796427999994,
                "created_at": "2025-04-18T07:05:42.630377Z"
            },
            "avail_value": 50111.961209249996,
            "unrealized_pnl": -7.605
        },
        {
            "basic": {
                "id": 3,
                "username": "test2",
                "email": "None",
                "fund": 122888.94780999995,
                "created_at": "2025-04-25T02:07:07.869628Z"
            },
            "avail_value": 122885.11259124996,
            "unrealized_pnl": -7.605
        },
        {
            "basic": {
                "id": 1,
                "username": "test01",
                "email": "<EMAIL>",
                "fund": 9.94,
                "created_at": "2025-04-18T07:05:42.630377Z"
            },
            "avail_value": 6.104781249999999,
            "unrealized_pnl": -7.605
        }
    ]
}
```


|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| id |i32| 金库ID |
| username | string| 用户名|
| email | string | 用户邮箱 |
| fund  | f64 | 余额|
| created_at | string | 创建时间|
| avail_value | string | 可用余额 |
| unrealized_pnl | f64 | 未实现盈亏|


## 信号创建

**POST** http://API_HOST:5800/apis/v0/dev/cs/post_create_signal

### body

### 必填项：

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| user_id |i32| 金库(策略组)ID, 每个交易员，联系后台管理，分配一个或多个金库ID使用 |
| username| string | 交易员名称 |
| uuid| string| 交易员uuid |
| signal_name| String| 信号名 |
| underlying_asset |string| 信号控制的币种 |


### 示例：
```json
{
    "user_id": 1,
    "username": "test",
    "uuid": "ec05c197-917d-48bb-99bf-367fb85364a2",
    "signal_name": "nezha-no1",
    "underlying_asset": "BTC"
}
```

### 返回：
```json
{
    "code": 200800000,
    "msg": {
        "signal_id": 4
    }
}
```

|字段 | 类型 | 描述 |
| -------- | --------- | -------|
| signal_id |i32| 信号ID |


## 获取信号列表

**GET** http://API_HOST:5800/apis/v0/dev/cs/get_signals

### body

空

### 返回：
```json
{
    "code": 200800000,
    "msg": [
        {
            "id": 2,
            "username": "test",
            "uuid": "ec05c197-917d-48bb-99bf-367fb85364a2",
            "signal_type": "candidate",
            "signal_name": "noname",
            "signal_pnl": 0.2300000000000182,
            "running_status": "closed",
            "consecutive_wins": 0,
            "subscribers": 0,
            "operation_direction": "close",
            "underlying_asset": "ETH",
            "monthly_return_rate": 1.037607004218656,
            "monthly_alpha": 0.0,
            "annual_win_rate": 1.0,
            "cumulative_income": 0.0,
            "sharpe_ratio": 0.0,
            "three_yield": 0.0,
            "seven_yield": 0.0,
            "max_drawdown_7days": 0.0,
            "running_time": 0.0,
            "historical_win_rate": 0.0,
            "profit_loss_count": 0.0,
            "profit_loss_ratio": 0.0,
            "confidence": "low",
            "evaluation_status": "unqualified",
            "review": "",
            "user_id": 1,
            "created_date": null,
            "updated_date": null
        },
        {
            "id": 3,
            "username": "test",
            "uuid": "ec05c197-917d-48bb-99bf-367fb85364a2",
            "signal_type": "candidate",
            "signal_name": "noname",
            "signal_pnl": 0.0,
            "running_status": "closed",
            "consecutive_wins": 0,
            "subscribers": 0,
            "operation_direction": "close",
            "underlying_asset": "BTC",
            "monthly_return_rate": 1.012138853013036,
            "monthly_alpha": 0.0,
            "annual_win_rate": 0.5,
            "cumulative_income": 0.0,
            "sharpe_ratio": 0.0,
            "three_yield": 0.0,
            "seven_yield": 0.0,
            "max_drawdown_7days": 0.0,
            "running_time": 0.0,
            "historical_win_rate": 0.0,
            "profit_loss_count": 0.0,
            "profit_loss_ratio": 0.0,
            "confidence": "low",
            "evaluation_status": "unqualified",
            "review": "",
            "user_id": 1,
            "created_date": null,
            "updated_date": null
        },
        {
            "id": 4,
            "username": "test",
            "uuid": "ec05c197-917d-48bb-99bf-367fb85364a2",
            "signal_type": "candidate",
            "signal_name": "noname",
            "signal_pnl": 0.0,
            "running_status": "closed",
            "consecutive_wins": 0,
            "subscribers": 0,
            "operation_direction": "unknown",
            "underlying_asset": "ADA",
            "monthly_return_rate": 1.0,
            "monthly_alpha": 0.0,
            "annual_win_rate": 0.0,
            "cumulative_income": 0.0,
            "sharpe_ratio": 0.0,
            "three_yield": 0.0,
            "seven_yield": 0.0,
            "max_drawdown_7days": 0.0,
            "running_time": 0.0,
            "historical_win_rate": 0.0,
            "profit_loss_count": 0.0,
            "profit_loss_ratio": 0.0,
            "confidence": "low",
            "evaluation_status": "unqualified",
            "review": "",
            "user_id": 1,
            "created_date": null,
            "updated_date": null
        }
    ]
}
```


返回json对象中，各个字段含义请参考**产品文档**