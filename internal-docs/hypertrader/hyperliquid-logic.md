# HyperLiquid 交易逻辑


## 1. 关键术语：

- **可用余额(available_value)**： 代表可以进行开仓，加减杠杆等需要扣除账户资金操作的资金总量
- **余额(account_value)**： 余额是用户金库中总余额，订单占用和仓位占用, 每次平仓后得到盈亏，重新计算余额
- **MAX_LEVERAGE**： 最大杠杆率，用于计算清仓价格，常量，不可改，BTC为40， ETH为25
- **leverage**: 杠杆率，和最大杠杆率不同，这个可以下单时设置，下单后可修改
- **entry_price**: 仓位的平均价格，开仓时entry_price就是买入价格，加仓后会计算加仓后的加权平均价格
- **mark_price**: 对应币种的当前价格
- **side**: 1为做多，-1为做空
- **position_size**: 仓位数量
- **margin_required**: 仓位所需保证金，margin_required=mark_price*position_size/leverage

- **maintenance_margin_required**： 仓位的维持保证金,maintenance_margin_required = margin_required*leverage/MAX_LEVERAGE/2 (全仓和逐仓都用这个)

```{r echo=FALSE, eval=FALSE}
 # **全仓**： maintenance_margin_required = margin_required*leverage/MAX_LEVERAGE/2
 # **逐仓**： maintenance_margin_required = margin_required/MAX_LEVERAGE/2
 ```

<!-- - **margin_available**: 可用保证金，公式： 
- - **全仓**： margin_available=account_value - maintenance_margin_required  
- - **逐仓**： margin_available=margin_required - maintenance_margin_required
 -->

## 2. 余额与可用余额计算： 
- **公式**:

- 1. 余额(account_value) = 初始余额
- 2. 可用余额 = 余额 - 订单占用 - 仓位占用

### 2.1 订单占用
    订单占用 = 限价单占用 + 市价单占用
    
#### 2.1.1 限价单占用
    限价单占用 = 订单Limit价格 * 订单买入数量 / leverage

#### 2.1.2 市价单占用
    市价单占用 = mark_price * (1 + 最大滑点) * 订单买入数量 / leverage

### 2.2 仓位占用
    仓位占用 = margin_required 

## 3. 逐仓清算价格计算： 

- **公式**： liq_price = mark_price - side * (account_value - maintenance_margin_required) / position_size / (1 - (1/MAX_LEVERAGE/2) * side)



## 4. 全仓清算价格计算：
- **公式**： liq_price = mark_price - side * (margin_required - total_maintenance_margin_required) / position_size / (1 - (1/MAX_LEVERAGE/2) * side)

- **total_maintenance_margin_required**: 代表所有全仓仓位的maintenance_margin_required值的总和


## 5. 杠杆修改：
- 1. 杠杠修改后会重新计算**仓位占用**，重新计算**可用余额**，即杠杆增大后，**可用余额减少**，反之则**可用余额**增加
- 2. 杠杆增加后若没有足够的可用余额，则修改失败

## 6. 加仓减仓计算： 
- 1. 加仓后重新加权计算entry_price
- 2. 减仓后计算盈亏，并重新计算**余额**，**可用余额**

## 7. 强平条件： 
- 1. 达到清算价格
- 2. 所有仓位的margin_required总和超过可用余额时，具体策略还在研究中

