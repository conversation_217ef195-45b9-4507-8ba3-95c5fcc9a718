# 信号（Signal）操作API

## 信号发生

调用端通过HTTP POST方法，下面每个字段，用json格式填充body

**POST** https://API_HOST/apis/v0/signal/generate



|Headers | Name |Type | Description|
| -------- | --------- | ------- |-----|
|Content-Type*|String | "application/json"|

### Request Body

| 字段      | 类型       | 说明     |
| -------- | --------- | ------- |
|id  | i32 |  序号，系统递增 |
|username | String| 交易员的昵称，这是交易员通过APP申请时填写的昵称；若通过线下邀请的，则昵称由商务同事手动录入；昵称支持修改；|
|uuid | i32| 交易员唯一标识，UUID是系统自动赋值；不支持修改； |
|signal_name| String | 交易员管理的信号名称；信号名称由XBIT命名；一个交易员支持管理多个信号；信号名称不能重复；支持修改；|
|operation_direction| String | 信号的操作方向，有两个值 ,做多, 做空|
|underlying_asset| String | 该信号控制的币种，如BTCUSD、ETHUSD等格式；|
|vault_name| String | 一个信号控制一个币种；而金库又包含币种，因此需要把信号与金库里的币种的对应关系列出来；如：哪吒3号-BTCUSD，这种格式 |
|vault_address| String | 一个信号控制一个币种；而金库又包含币种，因此需要把信号与金库的对应关系列出来；|
|signal_type| String | 开仓 open, 平仓 close |
|position_mode| String | 全仓 cross, 逐仓 isolate |
|price| f64 | 开仓价格 |
|size| f64 | 开仓数量 |
|tp_price| f64 | 止盈价格 |
|sp_price| f64 | 止损价格 |
|leverage| i32 | 杠杆率   |

### 说明：
每个信号由id 和 交易员uuid 唯一识别

### 返回：
```json
{
    "code": 200,
    "msg": ""
}
```

### 示例：




## 手动平仓
调用端通过HTTP POST方法，用json格式填充body， 如果是限价平仓，则会产生限价单，此时信号状态更新为**平仓中**，订单成交后，状态变成**已平仓**

**POST** https://API_HOST/apis/v0/signal/closeposition


|Headers | Name |Type | Description|
| -------- | --------- | ------- |-----|
|Content-Type*|String | "application/json"|

### Request Body

| 字段      | 类型       | 说明     |
| -------- | --------- | ------- |
|uuid | i32| 交易员唯一标识，UUID是系统自动赋值；不支持修改； |
|id  | i32 |  序号，系统递增 |
|price| f64 | 限价平仓价格，可选，不填price就是市价平仓|

### 返回：
```json
{
    "code": 200,
    "msg": ""
}
```


## 撤销信号
调用端通过HTTP POST方法，用json格式填充body，成功后信号被撤销

**POST** https://API_HOST/apis/v0/signal/cancel

|Headers | Name |Type | Description|
| -------- | --------- | ------- |-----|
|Content-Type*|String | "application/json"|

### Request Body

| 字段      | 类型       | 说明     |
| -------- | --------- | ------- |
|uuid | i32| 交易员唯一标识，UUID是系统自动赋值；不支持修改； |
|id  | i32 |  序号，系统递增 |

### 返回：
```json
{
    "code": 200,
    "msg": ""
}
```


## 调整杠杆倍数


调用端通过HTTP POST方法，用json格式填充body，成功后信号对应仓位的杠杆倍数将被更改

**POST** https://API_HOST/apis/v0/signal/update-leverage

|Headers | Name |Type | Description|
| -------- | --------- | ------- |-----|
|Content-Type*|String | "application/json"|

### Request Body

| 字段      | 类型       | 说明     |
| -------- | --------- | ------- |
|uuid | i32| 交易员唯一标识，UUID是系统自动赋值；不支持修改； |
|id  | i32 |  序号，系统递增 |
|leverage| i32 | 新杠杆率

### 返回：
```json
{
    "code": 200,
    "msg": ""
}
```


## 获取交易员对应信号列表


调用端通过HTTP GET方法，用json格式填充body，成功后返回该交易员发出的的信号

**GET** https://API_HOST/apis/v0/signal/list

|Headers | Name |Type | Description|
| -------- | --------- | ------- |-----|
|Content-Type*|String | "application/json"|

### Request Body

空

### 返回：

```json
{
    "code": 200,
    "msg": "",
    "signalList" : [
        {
            "id": 0,
            "username": "test",
            "state": "reach_tp"
        }
    ]
}
```


返回的signalList代表所有返回的信号，信号中的字段比/signal/generate(信号生成)多了一个state字段，代表信号当前的状态, 信号状态包含下面值：

- inital: 代表信号刚被创建，没被成交
- reach_tp: 已止盈
- reach_sp: 已止损
- closing:  平仓中
- closed:   已平仓
- running:  运行中
