# Signal 实现逻辑 



1、交易员调用**信号发生**接口，id由交易员生产，每调用**信号发生**递增id

2、信号在hypertrader生成后存入数据库，并生成相应订单，信号state初始化为inital，

3、订单成功交易前，交易员可以调用**撤销信号**接口，撤销信号

4、订单成功交易后，信号状态变成running

4、信号处于running状态时，交易员可以调用**手动平仓**，将对应仓位平仓，并且信号状态变成closing(平仓中) 或 closed(已平仓)

5、信号对应仓位达到止损和止盈价格平仓后，信号状态变为止盈/止损 

6、信号处于runing状态或inital状态时，该币种不能接受新信号，需要手动平仓，或者等待信号变成已止盈/已止损/已平仓后，才能接受新信号

7、信号处于running状态时，系统每天统计信号对应仓位的**仓位价值**，用于计算30天盈利，胜率等信息
