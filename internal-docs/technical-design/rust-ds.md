# HyperTrader 数据定义

## HFT Vault & Strategy / 高频交易金库及策略

此数据结构定义了高频交易机器人（HFT bot）的策略配置格式。

| 属性                         | 类型                | 描述                                                                                           | 示例                                                            |
| ---------------------------- | ------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------- |
| **strategy_uuid**            | string, unique      | 交易策略集群标识符                                                       | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **strategy_name**            | string              | 交易策略集群标识名称                                                     | `"XBIT智选2号"`                                                 |
| **vault_address**            | string, unique      | 金库钱包地址                                                                   | `"0xbbb47ca9882c87caa5629d91680c0ef3a57cedf8"`                   |
| **strategy_owner**           | string              | 策略所有者的钱包地址                                                   | `"0x2b2fBBe44c52ab8e6FEf1d4748C78388af171307"`                   |
| **preferred_strategy**       | boolean             | 优选策略将作为信号指标发送给前端                           | `True`                                                          |

---

## HFT Signal / 高频交易信号

此数据结构定义了来自高频交易机器人（HFT bot）的信号格式。

| 属性                         | 类型                | 描述                                                                                           | 示例                                                            |
| ---------------------------- | ------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------- |
| **id**                       | string, unique      | 信号 ID                                                                            | `"72f27700-6f1e-4c43-b036-a91ae37d6929"`                         |
| **strategy_uuid**            | string, foreign_key | 交易策略集群标识符                                                       | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **action**                   | string              | 表示该信号是“买入”还是“卖出”，允许值："buy", "sell"                                            | `"buy"`                                                         |
| **token**                    | string              | 交易标的的代币符号或交易对                                                    | `"ETH-USDT"`                                                    |
| **current_price**            | float               | 信号生成时的市场价格                                                          | `3842.75`                                                       |
| **entry_price**              | float               | 建议的建仓价格                                                               | `3840.00`                                                       |
| **take_profit**              | float               | 止盈目标价格                                                                 | `3900.00`                                                       |
| **stop_loss**                | float               | 止损价格                                                                     | `3800.00`                                                       |
| **position_size**            | float, optional     | 仓位名义价值（可以是 USD 或代币单位）                                           | `1000.0`                                                        |
| **leverage**                 | float, optional     | 使用的杠杆倍数                                                                  | `10.0`                                                          |
| **status**                   | string              | 信号的当前状态，允许值："pending", "executed", "cancelled", "expired"                           | `"pending"`                                                     |
| **is_position_closing_signal**| bool, optional      | 该信号是否为平仓信号                                                            | `True`                                                          |
| **signal_time**              | string              | 信号生成时间（ISO 8601 格式）                                   | `"2025-03-21T13:45:00Z"`                                          |
| **expires_at**               | string, optional    | 信号过期时间（ISO 8601 格式，可选）                              | `"2025-03-21T14:00:00Z"`                                          |
| **interval**                 | string, optional    | 信号所基于的时间周期，如 "1H", "4H", "1D"                                                     | `"1H"`                                                          |
| **metadata**                 | dict, optional      | 策略附加的自定义字段                                              | `&#123;"confidence": 0.95&#125;`                                            |

---

## Vault Token Position / 金库币种持仓

此数据结构定义了策略金库的每个币种持仓状态。

| 属性                         | 类型                | 描述                                                                                           | 示例                                                            |
| ---------------------------- | ------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------- |
| **id**                       | string, unique      | 新提交或变更仓位将生成新ID，确保记录每次操作。                                                  | `"72f27700-6f1e-4c43-b036-a91ae37d6929"`                         |
| **strategy_uuid**            | string, foreign_key | 策略标识符                                       | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **token**                    | string              | 币种符号                                                                     | `"BTC-USDT"`                                                    |
| **direction**                | string              | 持仓方向，允许值："long", "short"                                                               | `"long"`                                                        |
| **status**                   | string              | 当前状态，允许值："open", "closing", "closed", "expired"                                        | `"open"`                                                        |
| **position_size**            | float               | 当前名义仓位大小                                                                | `10000.0`                                                       |
| **entry_price**              | float               | 当前持仓建仓价格                                                                 | `27250.5`                                                       |
| **current_price**            | float               | 最新市场价格                                                                    | `27410.2`                                                       |
| **leverage**                 | float               | 杠杆倍数                                                                            | `8.0`                                                           |
| **collateral_used**          | float               | 分配至该仓位的资金                                                                | `1250.0`                                                        |
| **unrealized_pnl_usd**       | float               | 未实现盈亏                                                                         | `60.5`                                                          |
| **realized_pnl_usd**         | float               | 已实现盈亏                                                                         | `15.0`                                                          |
| **signal_id**                | string, optional    | 最后一个关联信号的ID                             | `"72f27700-6f1e-4c43-b036-a91ae37d6929"`                         |
| **opened_at**                | string              | 仓位开启时间（ISO 8601 格式）                                    | `"2025-03-21T13:45:00Z"`                                          |
| **updated_at**               | string              | 最后更新时间（ISO 8601 格式）                                    | `"2025-03-21T14:15:00Z"`                                          |

---

## Vault Metrics Summary / 金库总资金指标

此数据结构定义了策略金库的资金使用、风险敞口和交易指标汇总信息。

| 属性                              | 类型   | 描述                                                                       | 示例                                                            |
| --------------------------------- | ------ | -------------------------------------------------------------------------- | --------------------------------------------------------------- |
| **strategy_uuid**                 | string, foreign_key | 策略标识符                   | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **vault_value_usd**               | float  | 金库当前总价值                                                             | `15432.67`                                                      |
| **total_collateral**              | float  | 所有持仓的总抵押资产                                                       | `10000.0`                                                       |
| **available_balance**             | float  | 可用于新开仓位的资金                                                       | `5432.67`                                                       |
| **current_leverage**              | float  | 当前平均杠杆                                                               | `7.4`                                                           |
| **open_position_count**           | int    | 当前持仓数量                                                               | `3`                                                             |
| **total_position_volume_usd**     | float  | 当前总持仓的名义交易量                                                     | `25000.0`                                                       |
| **realized_pnl_usd**              | float  | 累计已实现盈亏                                                             | `-125.50`                                                       |
| **unrealized_pnl_usd**            | float  | 累计未实现盈亏                                                             | `432.80`                                                        |
| **cumulative_trading_volume_usd** | float  | 策略启动以来的累计交易量                                                   | `245000.0`                                                      |
| **cumulative_fees_usd**           | float  | 累计手续费支出                                                             | `185.76`                                                        |
| **updated_at**                    | string | 更新时间（ISO 8601 格式）                                                  | `"2025-03-21T14:20:00Z"`                                          |

---

## Vault Daily Snapshot / 金库每日快照

此数据结构定义了策略金库每日状态的快照，用于策略表现回测、趋势分析和可视化图表等。（UTC 时间）

| 属性                   | 类型   | 描述                                               | 示例                                                            |
| ---------------------- | ------ | -------------------------------------------------- | --------------------------------------------------------------- |
| **strategy_uuid**      | string, foreign_key | 策略金库标识符                                    | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **date**               | string | 快照日期（ISO 格式）                               | `"2025-03-21"`                                                  |
| **vault_value_usd**    | float  | 当日金库总价值                                     | `15823.00`                                                      |
| **realized_pnl_usd**   | float  | 当日已实现盈亏                                     | `225.50`                                                        |
| **unrealized_pnl_usd** | float  | 当日浮动盈亏                                       | `40.25`                                                         |
| **trading_volume_usd** | float  | 当日交易量                                         | `15000.00`                                                      |
| **open_position_count**| int    | 当日持仓数量                                       | `2`                                                             |
| **updated_at**         | string | 快照生成时间（ISO 8601 格式）                        | `"2025-03-21T23:59:59Z"`                                          |

---

## Vault Position History / 金库历史持仓记录

此数据结构定义了金库所有持仓生命周期的完整历史记录，便于策略回测、报表生成与风险分析。

| 属性                         | 类型                | 描述                                               | 示例                                                            |
| ---------------------------- | ------------------- | -------------------------------------------------- | --------------------------------------------------------------- |
| **position_id**              | string, unique      | 仓位唯一标识                                       | `"pos_eth_00087"`                                               |
| **strategy_uuid**            | string, foreign_key | 策略金库标识符                                     | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **token**                    | string              | 币种                                               | `"ETH-USDT"`                                                    |
| **direction**                | string              | 持仓方向，允许值："long", "short"                   | `"long"`                                                        |
| **entry_price**              | float               | 开仓价格                                           | `3140.00`                                                       |
| **exit_price**               | float               | 平仓价格                                           | `3190.00`                                                       |
| **position_size**            | float               | 仓位名义金额                                       | `2000.00`                                                       |
| **leverage**                 | float               | 杠杆倍数                                           | `10.0`                                                          |
| **collateral_used**          | float               | 使用的保证金                                       | `200.00`                                                        |
| **realized_pnl_usd**         | float               | 实现盈亏                                           | `50.00`                                                         |
| **signal_entry_id**          | string, foreign_key | 开仓信号 ID                                        | `"sig_123abc"`                                                  |
| **signal_exit_id**           | string, foreign_key, optional | 平仓信号 ID                           | `"sig_456def"`                                                  |
| **opened_at**                | string              | 开仓时间（ISO 8601 格式）                          | `"2025-03-20T09:00:00Z"`                                          |
| **closed_at**                | string              | 平仓时间（ISO 8601 格式）                          | `"2025-03-20T10:30:00Z"`                                          |

---

## Strategy Performance Metrics / 策略绩效评估

此数据结构定义了实盘和虚拟盘策略的绩效评估，包括统计胜率、收益、连胜等指标。

| 属性                         | 类型                | 描述                                                           | 示例                                                            |
| ---------------------------- | ------------------- | -------------------------------------------------------------- | --------------------------------------------------------------- |
| **strategy_uuid**            | string, foreign_key | 策略标识符                                                    | `"62c3b810-6ac9-49ea-b2cd-0e76a195f7a9"`                         |
| **mode**                     | string              | 评估类型，允许值："real", "virtual"                             | `"virtual"`                                                     |
| **win_rate**                 | float               | 胜率（盈利交易占比）                                            | `0.58`                                                          |
| **recent_month_return_pct**  | float               | 最近一个月收益（百分比）                                        | `6.2`                                                           |
| **total_return_pct**         | float               | 累计总收益（百分比）                                            | `32.4`                                                          |
| **live_return_pct**          | float               | 实时收益（浮动）                                                | `4.9`                                                           |
| **btc_outperformance_pct**   | float               | 最近一个月跑赢 BTC 现货的幅度                                   | `2.1`                                                           |
| **longest_win_streak**       | int                 | 连胜次数记录                                                  | `5`                                                             |
| **sharpe_ratio**             | float               | 夏普比率                                                       | `1.42`                                                          |
| **evaluation_period**        | string              | 评估周期（例如 "30d", "all"）                                   | `"30d"`                                                         |
| **updated_at**               | string              | 更新时间（ISO 8601 格式）                                        | `"2025-03-21T00:00:00Z"`                                          |

---

## Metrics Calculation References / 指标计算说明

## Sharpe Ratio（夏普比率）计算公式

$$
Sharpe\ Ratio = \frac&#123;R_p - R_f&#125;&#123;\sigma_p&#125;
$$

- **$R_p$** = 组合收益率  
- **$R_f$** = 无风险收益率  
- **$\sigma_p$** = 组合超额收益率的标准差

参考: [Investopedia: Sharpe Ratio](https://www.investopedia.com/terms/s/sharperatio.asp)

- **参数说明**:
  - `avg_daily_return`: 策略平均日收益率
  - `risk_free_rate`: 无风险利率（可设为 0 或 US Treasury 年化收益/365）
  - `std_daily_return`: 策略收益率的标准差

计算公式:

$$
sharpe\_ratio = \frac&#123;avg\_daily\_return - risk\_free\_rate&#125;&#123;std\_daily\_return&#125;
$$

## BTC Race Formular 跑赢大盘计算

$$
Outperformance = R_&#123;strategy\_30d&#125; - R_&#123;BTC\_30d&#125;
$$

- **$R_&#123;strategy\_30d&#125;$** = 策略近30天收益率  
- **$R_&#123;BTC\_30d&#125;$** = BTC现货近30天涨幅

---