# Rust HyperTrader 数据

## 1. 后台管理

- **金库管理**  
  - 添加金库、分配操作地址、删除金库  
  - 修改/添加交易员地址  
  - 查看金库最新持仓及可用金额

- **交易对管理**  
  - 批量导入交易对

- **其它管理功能**  
  - 金库历史持仓列表  
  - 币种信号列表  
  - 币种订单列表  
  - 金库快照列表  
  - 金库策略组评估表

---

## 2. 数据表关联关系

所有数据表之间的关联均通过名称后缀 `_id` 进行关联。

---

## 3. 数据表字段详情

### 3.1 金库-交易员-策略组相关信息表：`vault`

| 字段名称              | 描述                     |
| --------------------- | ------------------------ |
| `vault_name`          | 金库（策略组）名称       |
| `vault_address`       | 金库钱包地址             |
| `vault_owner_address` | 所有者钱包地址           |
| `is_signal`           | 是否优选推送             |

---

### 3.2 金库各个币种最新持仓及可用金额信息表：`vault_pairs`

| 字段名称                  | 描述                     |
| ------------------------- | ------------------------ |
| `symbol_id`             | 交易对 ID                |
| `symbol_available_amount` | 可用金额                 |
| `free_total`            | 手续费总额               |
| `amount`                | 持仓数量                 |

---

### 3.3 交易对列表：`pair`

| 字段名称      | 描述                         |
| ------------- | ---------------------------- |
| `symbol`      | 交易对名称                   |
| `is_tradable` | 是否允许进行交易下单         |
| `min_size`    | 最小下单数量                 |
| `max_size`    | 最大下单数量                 |

---

### 3.4 金库最新持仓表（包含所有交易对）：`vault_position`

| 字段名称   | 描述                                       |
| ---------- | ------------------------------------------ |
| `vault_id` | 持仓金库                                   |
| `symbol_id`| 持仓交易对                                 |
| `symbol`   | 持仓交易对名称                             |
| `type`     | 持仓类型（buy/sell）                        |
| `amount`   | 持仓数量                                   |
| `leverage` | 杠杆倍数                                   |

---

### 3.5 币种信号表：`symbol_signal`

| 字段名称            | 描述                                   |
| ------------------- | -------------------------------------- |
| `symbol_id`         | 信号交易对                             |
| `symbol`            | 信号交易对名称                         |
| `type`              | 信号方向（buy/sell）                   |
| `order_price`       | 开仓价格                               |
| `take_profit_price` | 止盈价格                               |
| `stop_loss_price`   | 止损价格                               |
| `leverage`          | 杠杆倍数                               |

---

### 3.6 订单表：`trade_orders`

| 字段名称          | 描述                                                |
| ----------------- | --------------------------------------------------- |
| `order_id`        | 订单 ID                                             |
| `created_date`    | 创建日期                                            |
| `udpated_date`    | 更新日期                                            |
| `symbol_id`       | 交易对 ID                                           |
| `vault_id`        | 下单账户所属金库                                    |
| `order_status`    | 订单状态                                            |
| `order_type`      | 市价单/限价单类型                                   |
| `price`           | 下单价格                                            |
| `amount`          | 成交量                                              |
| `trade_type`      | 下单类型（实盘订单、虚拟订单）                      |
| `trigger_price`   | 止盈止损触发价格                                    |
| `trigger_status`  | 止盈止损是否已经触发                                |
| `trigger_time`    | 止盈止损单触发时间                                  |
| `signal_id`       | 下单关联的信号                                      |

---

### 3.7 数据统计相关表

#### 3.7.1 金库每日快照：`snapshot`

| 字段名称           | 描述                                  |
| ------------------ | ------------------------------------- |
| `vault_id`         | 金库 ID                               |
| `date`             | 快照日期                              |
| `vault_value_usd`  | 当日总价值（美元）                    |
| `position_count`   | 当日持仓数量                          |
| `realized_pnl_usd` | 当日已实现盈亏（美元）                |
| `unrealized_pnl_usd`| 当日浮动盈亏（美元）                 |
| `trading_volume_usd`| 当日交易量（美元）                   |

#### 3.7.2 金库策略组评估表（单币种及整体）

| 字段名称       | 描述                         |
| -------------- | ---------------------------- |
| `vault_id`     | 金库 ID（虚拟盘为 0）         |
| `symbol_id`    | 交易对 ID                    |
| `win_rate`     | 胜率                         |
| `recent_pct`   | 近一个月收益                |
| `total_pct`    | 总收益                       |
| `sharpe_ratio` | 夏普比率                     |

---

## 4. 订单模块流程

1. **信号提交**  
   - 量化信号提交给下单模块，生成订单数据（可能包含止盈、止损信息）。

2. **订单触发**  
   - 后台服务根据交易对的最新价格，自动触发尚未触发止盈止损的订单。

---

## 5. 数据统计

- 统计表数据由定时任务计算后存储，包含每日金库快照、交易量、盈亏等关键指标。

---

## 6. 数据表关联关系

- 一个金库对应一个策略组。  
- 一个金库对应多个交易对。  
- 一个交易信号对应一个订单数据。  
- 一个金库中的一个交易对对应一个实时持仓数据。

---