# Rust HyperTrader 技术文档

本文档详细描述了基于 Rust 的 HyperTrader 模块在 XBIT 去中心化合约交易所中的实现方案。文档涵盖了核心概念定义、整体流程、关键技术要点。

---

## 1. 术语定义

- **第三方交易员**  
  统一指代交易员、量化团队、机器人、脚本或第三方团队。<br/>
  即所有通过 HyperTrader API 提交策略信号与下单请求的主体。

- **金库 / 策略组**  
  指一组针对多个币种的策略组合。在实盘环境下，一个策略组对应一个金库（Vault）；<br/>
  同一主钱包地址（或 strategy_owner）可拥有多个策略组（AI 策略组合）。

  **规则：**  
  - 一个策略组内，每个币种只能有一个活跃策略。  
  - 一个策略组可以包含同一币种的多个信号，<br/>但同一币种在同一金库内同一时刻只能存在一个活跃信号。  
  - 策略组的统计指标（如胜率）与单个信号的统计不同。

- **策略信号**  
  指第三方交易员提交的交易建议，包括买入或卖出信号。<br/>
  信号可分为实盘信号和虚拟盘信号，实盘信号用于下单；虚拟盘优选策略信号用于前端功能展示。

  **规则：**  
  - 第三方交易员提交的信号既可以用于虚拟盘策略，也可以用于实盘交易；  
  - 实盘信号将不作为信号推送至前端；  
  - 信号的统计（例如胜率）独立于策略组统计。

- **下单指令**  
  指用于执行交易的具体指令，依据 HyperLiquid API 标准构建。

  **规则：**  
  - 下单指令可以是市价单或限价单；  
  - 下单指令的执行采用多签名方式发起，多签签名数量决定权重，最高支持 10 个钱包；  
  - 提交下单前必须经过订单数据校验及风控处理。

---

## 2. 整体流程

### 2.1 策略组与金库创建

1. **策略组创建**  
   - 在提交任何策略信号前，系统要求第三方交易员先创建策略组。  
   - 策略组分为实盘策略组与虚拟盘策略组（虚拟盘策略组的默认 `strategy_owner` 为 `0x00000000000000000000000000`）。  
   - 每个策略组在实盘情况下对应一个金库，用于托管实际资金及执行实盘交易。

2. **金库管理**  
   - 一个主钱包地址可以创建多个金库（即多个策略组）。
   - 每个金库内同一币种仅允许有一个活跃订单方向（多头或空头），以防止重复信号干扰。

### 2.2 策略信号提交与处理

1. **信号提交**  
   - 第三方交易员（或自动化脚本/机器人）通过 HyperTrader API 提交策略信号。  
   - 信号可为实盘信号或虚拟盘信号，**只有虚拟盘信号支持标记为优选策略**。

2. **信号校验**  
   - 系统对提交的信号进行校验，确保同一币种在同一金库内不存在重复活跃信号。  
   - 信号数据包括交易对、方向、开仓价格、止盈止损价格、杠杆倍数、信号生成时间等信息。

3. **信号统计**  
   - 系统分别统计策略组的整体胜率和单个信号的胜率，用于后续评估和风控提示。

### 2.3 下单指令与多签执行

1. **下单指令生成**  
   - 当满足下单条件时（例如用户账户内有 ARB-USDC 或经过跨链兑换后获得 USDC），系统依据 HyperLiquid API 标准生成下单指令。  
   - 下单指令包含市价/限价、订单数量、价格、杠杆等关键信息。

2. **多签下单机制**  
   - 下单操作必须以多签名方式进行。所有参与多签的签名者数量决定交易的执行权重（最高支持 10 个钱包）。  
   - 多签机制确保交易员、程序员及代码本身均受到权限限制，保障老板资产安全。

3. **下单执行与反馈**  
   - Rust HyperTrader 模块调用 HyperLiquid SDK 发起下单操作，并等待成交确认。  
   - 对于实盘优选策略，下单成交后，信号信息会推送至前端供用户查看。

---

## 3. Rust HyperTrader

- 基于 HyperLiquid SDK 实现实盘交易功能，包括下单、撤单、查询订单状态等操作。
- 处理高频交易场景，通过多签名机制进行订单执行，确保交易安全。  
- 接收来自第三方交易员的信号，执行信号指标计算，并推送优选信号至前端。
- 统计量化策略的胜率与交易统计，供后续策略评估和优化使用。

---

## 4. 风险控制与权限管理

- **多签下单**：  
  通过多签机制，确保下单操作必须经过 10 个签名者中的足够数量确认，避免单一签名人失误或恶意操作。
  
- **权限隔离**：  
  为保护老板资产，系统严格区分交易员与开发人员的权限，确保程序代码仅具备必要操作权限，不得直接操控资金。

- **密钥管理**：  
  对于实盘交易涉及的 vault 金库，采取安全存储和密钥管理方案，必要时采用多签、MPC 或 TEE 等技术确保私钥安全。

---

## 5. 订单处理与统计

### 5.1 订单处理流程

1. **用户下单**：  
   用户在前端输入订单参数（币种、方向、杠杆、价格等），提交订单请求。

2. **后端订单校验与转发**：  
   后端验证订单数据后，将订单封装并通过 API 转发到 Rust 模块，由 Rust 模块调用 HyperLiquid API 执行订单。  
   - 若账户内无 ARB-USDC，则需先通过跨链模块进行资产兑换。

3. **订单执行与反馈**：  
   Rust 模块发起多签下单操作，等待成交确认，并实时反馈订单状态至后端及前端。

### 5.2 统计与评估

- **策略组统计**：  
  独立统计每个策略组的整体胜率、收益率、杠杆水平等指标，与单个信号统计区分开来。

- **信号统计**：  
  分析各个信号的胜率、执行效率及风险指标，供优选信号展示与后续策略调整参考。

---

## 6. 商业资金配置逻辑

为控制风控风险并实现策略优选机制，当前阶段金库（Vault）中的所有实盘资金将由 XBIT 官方提供，并基于证券行业基金经理常规考核制度，采用阶梯式资金释放机制。

该制度设计的核心目标为：
- 鼓励第三方交易员以可控风险运行策略并通过历史表现晋升；
- 在资金实盘执行前进行充分虚拟盘验证；
- 保障平台资金安全与收益稳定性。

### 6.1 资金提供规则

- 所有新建策略组将默认为「虚拟盘策略组」，无实盘资金支持，仅用于前端回测与观察。
- 策略组在一定观察期内，须依照预设规则执行全部信号，包括止盈与止损，不能中途干预。
- 当策略组与信号均达到平台制定的表现标准（见下方阶梯制度）后，系统将自动升级为实盘策略组，进入资金实盘分配流程。

### 6.2 阶梯式资金分配机制

以下为参考证券行业基金评级、CTA 策略管理制度拟定的「胜率-资金释放阶梯」：

| 等级 | 胜率要求     | 最近信号数量 | 最近 30 天回撤限制 | 可获资金支持（USD） | 状态说明           |
|------|--------------|----------------|---------------------|----------------------|--------------------|
| LV.0 | 初始          | ≥ 10             | -                   | 0                    | 默认观察状态         |
| LV.1 | ≥ 55%        | ≥ 20             | ≤ 10%               | 1,000                | 准入实盘试投阶段     |
| LV.2 | ≥ 60%        | ≥ 50             | ≤ 8%                | 5,000                | 小额资金阶段         |
| LV.3 | ≥ 65%        | ≥ 100            | ≤ 6%                | 20,000               | 中额金库试运营       |
| LV.4 | ≥ 70%        | ≥ 200            | ≤ 5%                | 50,000               | 高资金信任等级       |
| LV.5 | ≥ 75%        | ≥ 300            | ≤ 4%                | ≥ 100,000            | 可申请金库开放给用户买入 |

> ⚠️ **说明**：  
> - 胜率统计以策略信号为单位，策略组整体胜率用于评估整体逻辑一致性；
> - 所有虚拟盘将以标准虚拟总金额 10,000USD; 下单仓位 1,000U 10x 杠杆为基础；以保证数据的一致性；
> - 回撤计算基于 Vault 金库的浮动资金变化；
> - 达到任意阶段需持续满足标准，不达标将触发降级与限仓机制；
> - 达 LV.2 后需申请多签升级，开启更多权限。

### 6.3 优选信号与资金关联机制

- 虚拟盘信号可标记为「优选信号」，在满足稳定胜率与低回撤条件后才可被系统采纳并推送前端。
- 实盘优选信号要求：先执行下单且成交成功，系统才推送至前端用于展示。
- 平台将为实盘设立更高执行优先级与更高资金杠杆上限（如：3 倍 vs. 1 倍）；后台管理操作。

---