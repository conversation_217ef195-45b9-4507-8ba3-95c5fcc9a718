# Telegram 登陆架构

![Auth Service of meme](/img/technical-design/AuthService.drawio.png)
该图示展示了一个去中心化应用的注册与交易签名流程，采用 **Azure Key Vault** 和 **Trusted Execution Environment (TEE)** 保护用户密钥安全。在去中心化交易所，Telegram登陆模块将延用meme交易所的架构。注意：**该架构仅运用于 Telegram 授权登陆**，系统为用户托管钱包；WalletConnect 及钱包连接方法将是用户自行托管 (self-custody)。整体流程可分为以下几个主要步骤：

<table class="evenly-spaced">
  <tr>
    <td>![Landing page of meme](/img/technical-design/0.0unsigned-.png)</td>
    <td>![Binging TG](/img/technical-design/0.0unsigned-1.1tg-binding.png)</td>
    <td>![Bot generated wallets](/img/technical-design/telegram-logon.png)</td>
  </tr>
</table>

⚠️ 注意：HyperLiquid 邮箱注册使用 [EOA](https://web3.bitget.com/en/academy/understanding-eoa-wallets)。我们不需要支持邮箱登陆，仅需 Telegram、钱包链接、WalletConnect 登陆。

1. **用户注册（Telegram）**  
   1. 用户通过 Telegram 进行注册，服务端（Auth Service）验证后返回 Access Token。  
   2. （可选）该 Access Token 用于后续请求鉴权，确保用户身份合法。

2. **请求创建钱包**  
   1. 用户端（或前端）向 Auth Service 发起“创建钱包”请求。  
   2. Auth Service 调用 **Wallet Service**，通过 Azure Key Vault（存储 RSA 公私钥）或其他机制生成加密后的私钥与地址，并返回给用户。  
   3. 生成的**私钥**是被加密存储，避免明文暴露。

3. **存储用户信息 & 加密私钥**  
   1. Auth Service 将用户信息（包括地址、加密私钥等）写入后端数据库（Auth Database）。  
   2. 数据库可部署在 **AKS (Azure Kubernetes Service)** 中，以实现弹性扩容与高可用。

4. **获取用户私钥（解密流程）**  
   1. 当用户需要进行链上交易时，Auth Service 会调用 **Azure Key Vault** 或 TEE，使用 RSA 私钥解密用户的加密私钥。  
   2. 在 **Trusted Execution Environment (TEE)** 内部执行解密操作，确保私钥在内存中明文暴露的风险降至最低。

5. **交易签名**  
   1. 用户端提交“请求签名交易”时，Auth Service 会在安全环境内获取解密后的私钥或使用密钥进行签名。  
   2. **Wallet Service** 将完成实际的交易签名，并将签名后的交易返回给用户。  
   3. 用户再将已签名交易提交到区块链（Onchain）进行广播与执行。

---

## 流程分解

1. **注册阶段**  
   - 用户通过 Telegram 进行注册，获得 Access Token。  
   - 系统为用户在后台生成一对密钥（可能使用 RSA 或 ECC），并将私钥加密后存储。

2. **创建钱包**  
   - 用户调用 Auth Service 生成地址与加密私钥；  
   - 数据写入 Auth Database，便于后续检索与管理。

3. **解密 & 签名**  
   - 交易前需要从数据库中提取加密私钥；  
   - 通过 Azure Key Vault & TEE 解密私钥，或在 TEE 中直接进行签名操作；  
   - 签名结果返回给用户，由用户或系统将交易提交到区块链上。

4. **安全防护**  
   - **Azure Key Vault**：集中存储和管理 RSA 公私钥，防止密钥泄露；  
   - **TEE (Trusted Execution Environment)**：提供隔离的执行环境，对私钥进行解密和签名，避免在主机内存中留有明文私钥。

---

## 关键要点

1. **私钥加密与解密**  
   - 私钥从生成到使用的全流程都在安全环境（Key Vault / TEE）内进行，减少明文泄露风险。  
   - 只有在交易签名时才临时解密或在安全环境内执行签名。

2. **访问控制**  
   - 用户通过 Telegram 注册后获取 Access Token，用于后续与 Auth Service 的交互鉴权。  
   - Auth Service 需验证用户权限后才允许调用解密或签名流程。

3. **可扩展性**  
   - 通过 AKS 部署可弹性扩容，数据库与服务均可水平扩展。  
   - TEE 和 Key Vault 可独立扩容，满足高并发场景。

4. **安全合规**  
   - 全流程遵循最小权限原则：只有在需要时才解密私钥，或在 TEE 中执行签名。  
   - Key Vault 存储 RSA 公私钥，并可进行审计、访问控制等。

---