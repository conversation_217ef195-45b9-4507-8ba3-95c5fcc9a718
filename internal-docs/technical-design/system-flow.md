# 系统流程

## 1. 身份验证及跨链兑换流程

![Authentication](/img/technical-design/UserLogin&CrossChainExchange.png)

---

## 2. 前端流程

![FrontendDEX](/img/technical-design/FrontendDex.png)

```
---
config:
  layout: fixed
---
flowchart TD
 subgraph Trading_Flow["Trading Flow / 交易流程"]
        C["Receive Signals from Rust HyperTrader / 从Rust HyperTrader接收交易信号"]
        B["Access Frontend DEX - Backed by Hyperliquid API<br>访问前端 DEX - 基于 Hyperliquid API"]
        D["Display Signals on UI - Buy/Sell, Price, TP/SL based on token / UI 展示基于代币交易信号 - 买/卖, 价格, TP/SL"]
        E["User selects One-Click Trade or Self Trade / 用户选择一键交易或自主交易"]
        F["Place Order via Frontend DEX / 通过前端DEX下单交易"]
  end
 subgraph Vault_Participation["Vault Participation / 参与金库"]
        G["Receive Vault List from Rust HyperTrader API - with Winning Rate / 从Rust HyperTrader API接收金库列表 - 含胜率"]
        H["Display Vault Options on UI / UI展示金库选项"]
        I["User selects desired Vault / <br>用户选择参与金库"]
        J["Transfer Funds from Perps Wallet to Vault / 从Perps钱包转账资金至金库"]
        K["User can Exit Vault anytime (Aware fund-lock period) / 用户可随时退出金库 (有锁仓期)"]
  end
    A["Funds in Perps Wallet / Perps钱包内资金"] --> B
    B --> C & G
    C --> D
    D --> E
    E --> F
    G --> H
    H --> I
    I --> J
    J --> K

```

---

## 2. Rust独立模块流程

![RustFlow](/img/technical-design/RustFlow.png)

```
flowchart TD
    %% Signal Reception and Validation / 信号接收与校验
    A[Third Party Trader Submits Command / 第三方交易员提交指令]
    A --> B[Receive Command via HyperTrader API / 通过 HyperTrader API 接收指令]
    B --> C[Validate Signal Data / 校验信号数据]
    C --> D{Is Signal Valid? / 信号是否有效？}
    D -- No --> E[Reject Signal & Return Error / 拒绝信号并返回错误]
    D -- Yes --> F[Store Signal & Update Signal Store / 存储信号并更新信号库]

    %% Branch into Two Independent Flows / 分支为两个独立流程
    F --> G[Trigger Real Order Execution Flow / 启动实盘下单流程]
    F --> H[Trigger Virtual Simulation Module / 启动虚拟账户模拟模块]

    %% Module 1: Real Order Execution Flow / 模块1：实盘下单流程
    subgraph Real_Order_Execution [Real Order Execution / 实盘下单流程]
        G --> I[Generate Order Instruction / 生成下单指令]
        I --> J[Perform Risk Control & Data Verification / 执行风控与数据校验]
        J --> K[Initiate Multi-sig Order Execution via HyperLiquid SDK / 通过 HyperLiquid SDK 启动多签下单]
        K --> L[Place & Manage Order on HyperLiquid Vault / 在 HyperLiquid 金库下单并管理订单]
        L --> M[Receive Order Execution Feedback / 接收下单执行反馈]
        M --> N[Extract Actual Order Outcomes / 提取实盘订单结果]
    end

    %% Module 2: Virtual Simulation Flow / 模块2：虚拟账户模拟流程
    subgraph Virtual_Simulation [Virtual Simulation / 虚拟账户模拟]
        H --> O[Simulate Order Execution in Virtual Account / 在虚拟账户中模拟下单执行]
        O --> P[Evaluate Simulated Outcomes / 评估模拟结果]
        P --> Q[Extract Simulation Outcomes / 提取虚拟订单结果]
    end

    %% Unified Statistics Calculation / 统一统计计算模块
    N --> R[Calculate Combined Winning Rate & Performance Metrics / 计算实盘及虚拟订单综合胜率及绩效指标]
    Q --> R
    R --> S[Store & Update Combined Statistics / 存储并更新综合统计数据]
    S --> T[Report Combined Statistics for Risk Management / 上报综合统计数据用于风控]
```