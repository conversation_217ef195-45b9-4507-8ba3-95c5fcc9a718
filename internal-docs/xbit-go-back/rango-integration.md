# Rango 跨链兑换服务集成文档

## 概述

本文档描述了如何集成 Rango 跨链兑换服务。Rango 提供了完整的跨链兑换解决方案，支持多个区块链网络之间的资产兑换。

### Rango 集成流程图
![集成流程图](../../static/img/technical-design/rango-integration-image.png)



## API 接口说明

### 1. 获取兑换元数据 (getExchangeMeta)

获取支持的区块链、代币和交易所信息。

#### 请求
```bash
curl --location '${environment}/v1/getExchangeMeta' \
--data ''
```

#### 响应结构
```json
{
  "code": 0,
  "data": {
    "blockchains": [
      {
        "name": "BSC",
        "displayName": "BNB Chain",
        "chainId": "56",
        "logo": "https://raw.githubusercontent.com/rango-exchange/assets/main/blockchains/BSC/icon.svg"
      }
      // ... 其他支持的区块链
    ],
    "popularTokens": [
      {
        "symbol": "USDT",
        "name": "Tether",
        "address": "******************************************",
        "blockchain": "BSC",
        "decimals": 18,
        "image": "https://rango.vip/tokens/ALL/USDT.png"
      }
      // ... 其他热门代币
    ],
    "swappers": [
      {
        "swapperId": "PancakeSwapBsc",
        "title": "PancakeSwap",
        "logo": "https://raw.githubusercontent.com/rango-exchange/assets/main/swappers/Pancake/icon.svg",
        "swapperType": "DEX"
      }
      // ... 其他支持的交易所
    ],
    "tokens": [
      {
        "symbol": "BNB",
        "name": "BNB",
        "address": "",
        "blockchain": "BSC",
        "decimals": 18,
        "image": "https://rango.vip/tokens/ALL/BNB.png"
      }
      // ... 其他支持的代币
    ]
  },
  "msg": "success"
}
```

### 2. 获取所有可能的兑换路径 (getAllPossibleRoutes)

获取所有可能的跨链兑换路径，包括不同交易所和桥接方案。

#### 请求参数说明
- `fromBlockchain`: 源区块链
- `fromSymbol`: 源代币符号
- `fromTokenAddress`: 源代币地址（可选）
- `toBlockchain`: 目标区块链
- `toSymbol`: 目标代币符号
- `toTokenAddress`: 目标代币地址（可选）
- `amount`: 兑换数量
- `slippage`: 滑点容忍度

#### 请求
```bash
curl --location '${environment}/v1/getAllPossibleRoutes' \
--header 'content-type: application/json' \
--data '{
    "fromBlockchain": "BSC",
    "fromSymbol": "BNB",
    "fromTokenAddress": "",
    "toBlockchain": "POLYGON",
    "toSymbol": "USDC",
    "toTokenAddress": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359",
    "amount": "0.001",
    "slippage": "0.3"
}'
```

#### 响应结构
```json
{
    "code": 0,
    "data": {
        "from": {
            "blockchain": "BSC",
            "symbol": "BNB",
            "address": ""
        },
        "to": {
            "blockchain": "POLYGON",
            "symbol": "USDC",
            "address": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359"
        },
        "requestAmount": "0.001",
        "routeId": "03a308b9-7f03-4d4d-9480-d5bbdd8de31c",
        "results": [
            {
                "requestId": "c5a202d8-1a73-469b-9d35-1d79911df939",
                "outputAmount": "0.592959",
                "swaps": [
                    {
                        "swapperId": "YBridge Aggregator",
                        "swapperLogo": "https://raw.githubusercontent.com/rango-exchange/assets/main/swappers/XY Finance/icon.svg",
                        "swapperType": "AGGREGATOR",
                        "from": {
                            "symbol": "BNB",
                            "logo": "https://rango.vip/tokens/ALL/BNB.png",
                            "blockchainLogo": "https://raw.githubusercontent.com/rango-exchange/assets/main/blockchains/BSC/icon.svg",
                            "address": "",
                            "blockchain": "BSC",
                            "decimals": 18,
                            "usdPrice": 606.57
                        },
                        "to": {
                            "symbol": "USDC",
                            "logo": "https://rango.vip/i/mh9zko",
                            "blockchainLogo": "https://raw.githubusercontent.com/rango-exchange/assets/main/blockchains/POLYGON/icon.svg",
                            "address": "0x2791bca1f2de4661ed88a30c99a7a9449aa84174",
                            "blockchain": "POLYGON",
                            "decimals": 6,
                            "usdPrice": 0
                        },
                        "fromAmount": "0.001000000000000000",
                        "toAmount": "0.592985",
                        "fee": [
                            {
                                "asset": {
                                    "blockchain": "BSC",
                                    "symbol": "BNB",
                                    "address": ""
                                },
                                "expenseType": "FROM_SOURCE_WALLET",
                                "amount": "0.000656531200000000",
                                "name": "Network Fee",
                                "meta": {
                                    "type": "EvmNetworkFeeMeta",
                                    "gasLimit": "505024",
                                    "gasPrice": "1300000000"
                                },
                                "price": 606.57
                            }
                        ],
                        "estimatedTimeInSeconds": 300,
                        "swapChainType": "INTER_CHAIN",
                        "internalSwaps": [
                            // ... 内部兑换步骤
                        ]
                    }
                ],
                "resultType": "OK",
                "scores": [
                    {
                        "preferenceType": "PRICE",
                        "score": 94
                    },
                    {
                        "preferenceType": "NET_OUTPUT",
                        "score": 100
                    },
                    {
                        "preferenceType": "SPEED",
                        "score": 49
                    },
                    {
                        "preferenceType": "FEE",
                        "score": 100
                    },
                    {
                        "preferenceType": "SMART",
                        "score": 100
                    }
                ],
                "tags": [
                    {
                        "label": "Lowest Fee",
                        "value": "LOWEST_FEE"
                    },
                    {
                        "label": "Recommended",
                        "value": "RECOMMENDED"
                    }
                ]
            }
            // ... 其他可能的兑换路径
        ]
    },
    "msg": "success"
}
```

### 3. 获取最佳兑换路径 (getBestRoute)

获取最优的跨链兑换路径。

#### 请求参数说明
- `fromBlockchain`: 源区块链
- `fromSymbol`: 源代币符号
- `fromTokenAddress`: 源代币地址（可选）
- `toBlockchain`: 目标区块链
- `toSymbol`: 目标代币符号
- `toTokenAddress`: 目标代币地址（可选）
- `amount`: 兑换数量
- `slippage`: 滑点容忍度
- `fromAddress`: 源地址
- `toAddress`: 目标地址

#### 请求
```bash
curl --location '${environment}/v1/getBestRoute' \
--header 'content-type: application/json' \
--data '{
    "fromBlockchain": "BSC",
    "fromSymbol": "BNB",
    "fromTokenAddress": "",
    "toBlockchain": "POLYGON",
    "toSymbol": "USDC",
    "toTokenAddress": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359",
    "amount": "0.001",
    "slippage": "0.3",
    "fromAddress":"******************************************",
    "toAddress":"******************************************"
}'
```

#### 响应结构
```json
{
    "code": 0,
    "data": {
        "from": {
            "blockchain": "BSC",
            "symbol": "BNB",
            "address": "",
            "decimals": 18,
            "image": "https://rango.vip/tokens/ALL/BNB.png"
        },
        "to": {
            "blockchain": "POLYGON",
            "symbol": "USDC",
            "address": "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359",
            "decimals": 6,
            "image": "https://rango.vip/tokens/ALL/USDC.png"
        },
        "requestAmount": "0.001",
        "requestId": "1bfad897-82c1-4384-8d11-eb639404387a",//数据唯一标识
        "result": {
            "outputAmount": "0.593156",
            "swaps": [
                {
                    "swapperId": "PancakeSwapBsc",
                    "fromAmount": "0.001",
                    "toAmount": "0.605126",
                    "fee": [
                        {
                            "asset": {
                                "blockchain": "BSC",
                                "symbol": "BNB"
                            },
                            "amount": "0.000221",
                            "name": "Network Fee"
                        }
                    ],
                    "estimatedTimeInSeconds": 30
                }
                // ... 其他兑换步骤
            ]
        }
    },
    "msg": "success"
}
```

### 4. 确认兑换路径 (confirmRoute)

确认选择的兑换路径并验证钱包地址。

#### 请求参数说明
- `selectedWallets`: 选择的钱包列表
- `destination`: 目标地址
- `requestId`: 请求ID（数据唯一标识）

#### 请求
```bash
curl --location '${environment}/v1/confirmRoute' \
--header 'content-type: application/json' \
--data '{
  "selectedWallets": [
    {
      "blockchain": "BSC",
      "address": "******************************************"
    },
     {
      "blockchain": "ARBITRUM",
      "address": "******************************************"
    },
     {
      "blockchain": "POLYGON",
      "address": "******************************************"
    }
  ],
  "destination": "******************************************",
  "requestId": "c5a202d8-1a73-469b-9d35-1d79911df939"
}'
```

#### 响应
```json
{
    "code": 0,
    "data": {
        "requestId": "c5a202d8-1a73-469b-9d35-1d79911df939",
        "status": "success",
        "validationStatus": [
            {
                "blockchain": "BSC",
                "status": true,
                "message": "Wallet validated successfully"
            }
            // ... 其他区块链的验证状态
        ]
    },
    "msg": "success"
}
```

### 5. 创建交易 (createTx)

创建兑换交易。

#### 请求参数说明
- `requestId`: 请求ID
- `step`: 当前步骤
- `userSettings`: 用户设置
  - `slippage`: 滑点
  - `infiniteApprove`: 是否无限授权
- `validations`: 验证选项
  - `balance`: 余额验证
  - `fee`: 手续费验证
  - `approve`: 授权验证

#### 请求
```bash
curl --location '${environment}/v1/createTx' \
--header 'content-type: application/json' \
--data '{
    "requestId": "c5a202d8-1a73-469b-9d35-1d79911df939",
    "step": 1,
    "userSettings": {
      "slippage": 0.3,
      "infiniteApprove": true
    },  
    "validations": {
      "balance": true,
      "fee": true,
      "approve": true
    }
  }'
```

#### 响应
```json
  {
    "code": 0,
    "data": {
        "ok": true,
        "errorCode": 0,
        "traceId": 0,
        "transaction": {
            "type": "EVM",
            "blockChain": "BSC",
            "from": "******************************************",
            "to": "0x69460570c93f9DE5E2edbC3052bf10125f0Ca22d",
            "data": "0x6b39e82b...", // 交易数据 前端获取此数据与链进行交互
            "value": "0x38d7ea4c68000",
            "gasLimit": "0x7b4c0",
            "gasPrice": "1300000000"
        }
    },
    "msg": "success"
}
```

### 6. 检查授权状态 (checkApproval)

检查代币授权状态。

#### 请求参数说明
- `txId`: 交易ID
- `apiKey`: API密钥

#### 请求
```bash
curl --location 'https://api.rango.exchange/tx/b3a12c6d-86b8-4c21-97e4-809151dd4036/check-approval?txId=0x0dc418161c53087eb78b7f68ee66b06c98955b53953b82cc88c3ecc97d7b468f&apiKey=a0bea458-d1e7-4f7a-b65f-3af7c81806c2'
```

#### 响应
```json
{
    "isApproved": true,
    "txStatus": "PENDING",
    "currentApprovedAmount": "0.001",
    "requiredApprovedAmount": "0.001",
    "approvalTxHash": "0x0dc418161c53087eb78b7f68ee66b06c98955b53953b82cc88c3ecc97d7b468f"
}
```

### 7. 交易回调 (callBack)

通知交易状态更新。

#### 请求参数说明
- `requestId`: 请求ID
- `step`: 当前步骤
- `txHash`: 交易哈希

#### 请求
```bash
curl --location '${environment}/v1/callBack' \
--header 'content-type: application/json' \
--data '{
    "requestId":"42cb74d2-5da9-4ffd-a3b1-c3430326766b",
    "step": 1,
    "txHash":"0xb296395400872c5e9850840db7172098e83f9f369aa917c90fcce0469e7badc6"
  }'
```

#### 响应
```json
  {
    "code": 0,
    "data": {
        "success": "success",
        "nextStep": 2,
        "status": "PENDING",
        "message": "Transaction received and processing"
    },
    "msg": "success"
}
```

### 8. 获取交易历史 (getHistories)

查询用户的交易历史记录。

#### 请求参数说明
- `address`: 钱包地址
- `blockchain`: 区块链

#### 请求
```bash
curl --location '${environment}/v1/getHistories' \
--header 'content-type: application/json' \
--data '{
    "address":"******************************************",
    "blockchain": "BSC"
      }'
```

#### 响应
```json
{
    "code": 0,
    "data": {
        "total": 1,
        "list": [
            {
                "requestId": "e718c0ec-663b-4e81-a086-f644b3394ffe",
                "fromBlockchain": "BSC",
                "fromSymbol": "BNB",
                "toBlockchain": "BSC",
                "toSymbol": "USDT",
                "requestAmount": "0.001",
                "outputAmount": "0.580990",
                "status": "COMPLETED",
                "timestamp": 1745287652000,
                "swaps": [
                    {
                        "swapperId": "PancakeSwapBsc",
                        "fromAmount": "0.001",
                        "toAmount": "0.580990",
                        "txHash": "0xb296395400872c5e9850840db7172098e83f9f369aa917c90fcce0469e7badc6"
                    }
                ]
            }
            // ... 其他历史记录
        ]
    },
    "msg": "success"
}
```

### 9. 检查交易状态 (checkStatus)

查询交易的最新状态。step到next step需要checkStatus,成功的话才能执行next step

#### 请求参数说明
- `requestId`: 请求ID
- `txId`: 交易ID
- `step`: 当前步骤

#### 请求
```bash
curl --location '${environment}/v1/checkStatus' \
--header 'content-type: application/json' \
--data '{
  "requestId":"42cb74d2-5da9-4ffd-a3b1-c3430326766b",
  "txId": "0xe3985c0efb3663258eb98c0e1c0679bb4de67cae6fbf539d6d5b020d3bae9950",
  "step": 1
}'
```

#### 响应
```json
{
    "code": 0,
    "data": {
        "status": "success",
        "extraMessage": null,
        "failedType": null,
        "timestamp": 1745287652000,
        "outputAmount": "0.589658",
        "explorerUrl": [
            {
                "url": "https://bscscan.com/tx/0xb296395400872c5e9850840db7172098e83f9f369aa917c90fcce0469e7badc6",
                "description": "Inbound"
            },
            {
                "url": "https://arbiscan.io/tx/0xbe68e67536631ab6d4ddb443dab0d28fdbd9903dc465d13c3fe7ccf7e9e8b978",
                "description": "Outbound"
            }
        ],
        "referrals": [
            {
                "amount": "1500000000000",
                "blockChain": "BSC",
                "symbol": "BNB",
                "type": "RANGO"
            }
        ],
        "outputToken": {
            "blockchain": "ARBITRUM",
            "symbol": "USDT",
            "address": "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9",
            "decimals": 6
        },
        "outputType": "DESIRED_OUTPUT",
        "bridgeExtra": {
            "requireRefundAction": false,
            "srcTx": "0xb296395400872c5e9850840db7172098e83f9f369aa917c90fcce0469e7badc6",
            "destTx": "0xbe68e67536631ab6d4ddb443dab0d28fdbd9903dc465d13c3fe7ccf7e9e8b978"
        }
    },
    "msg": "success"
}
```


## 注意事项

1. 所有金额相关参数都需要使用字符串类型
2. 地址参数需要符合对应区块链的地址格式
3. 建议在交易前先调用 `getExchangeMeta` 获取支持的代币和交易所信息
4. 跨链交易可能需要多次授权，请确保钱包有足够的代币支付手续费
5. 建议设置合理的滑点，避免交易失败


rango worker 设计思路
说明：为了保证rango Api流畅，容错率高而设计
目前应用于checkStatus
原因：checkStatus 用户主动发起对rango的请求，并且请求很频繁失败的次数高，会导致前端流程失败
思路：用户请求交与worker处理，用户只拿结果
1. api -> worker -> request to rango -> cache result 
2. api-> cache result
3. api-> cache result
4. ...
5. api ->cache result is  success save the result to database 



