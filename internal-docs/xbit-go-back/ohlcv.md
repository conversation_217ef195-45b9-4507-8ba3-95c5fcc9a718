# OHLC Design
![OHLC System flow design](../../static/img/technical-design/OHLC_Design.png)

- OHLC data is stored in ClickHouse
- OHLC Work is responsible for updating OHLC real-time and push MQTT updated data to client
- <PERSON><PERSON> Crawler is responsible for crawling old data from Hyperliquid. There are 2 ways for this:
  - Crawl thru Hyperliquid API (limited old data)
  - Crawl thru Historical Market data in s3, extract and build OHLC data (fully history data, but high cost of consuming time and s3 data)

# OHLC Client integration interface

## 时间间隔映射
```go
var IntervalMap = map[string]string{
    "ONE_MINUTE":      "1m",
    "THREE_MINUTES":   "3m",
    "FIVE_MINUTES":    "5m",
    "FIFTEEN_MINUTES": "15m",
    "THIRTY_MINUTES":  "30m",
    "ONE_HOUR":        "1h",
    "TWO_HOURS":       "2h",
    "FOUR_HOURS":      "4h",
    "EIGHT_HOURS":     "8h",
    "TWELVE_HOURS":    "12h",
    "ONE_DAY":         "1d",
    "THREE_DAYS":      "3d",
    "ONE_WEEK":        "1w",
    "ONE_MONTH":       "1M",
}
```

## GraphQL 查询

```graphql
query GetOHLC($input: OHLCRequest!) {
    getOHLC(input: $input) {
        data {
            timestamp
            open
            close
            high
            low
            volume
        }
        symbol
    }
}
```

### 请求示例
```json
{
    "input": {
        "interval": "ONE_HOUR",
        "symbol": "ONDO",
        "fromTimeStamp": 1748432970000,
        "limit": 10000
    }
}
```

### 请求参数说明
- **interval**: 时间间隔，支持以下值：
  - `ONE_MINUTE`: 1分钟
  - `THREE_MINUTES`: 3分钟
  - `FIVE_MINUTES`: 5分钟
  - `FIFTEEN_MINUTES`: 15分钟
  - `THIRTY_MINUTES`: 30分钟
  - `ONE_HOUR`: 1小时
  - `TWO_HOURS`: 2小时
  - `FOUR_HOURS`: 4小时
  - `EIGHT_HOURS`: 8小时
  - `TWELVE_HOURS`: 12小时
  - `ONE_DAY`: 1天
  - `THREE_DAYS`: 3天
  - `ONE_WEEK`: 1周
  - `ONE_MONTH`: 1月
- **symbol**: 交易对名称（与 USD 配对）
- **fromTimeStamp**: 开始时间戳（毫秒）
- **limit**: 最大返回记录数（最大 10000）

### 响应字段说明
- **data**: OHLC 数据数组
  - **timestamp**: 时间戳（毫秒）
  - **open**: 开盘价
  - **close**: 收盘价
  - **high**: 最高价
  - **low**: 最低价
  - **volume**: 交易量（以资产符号为单位）
- **symbol**: 交易对名称
