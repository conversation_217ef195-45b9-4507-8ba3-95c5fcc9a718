# Rango Worker 设计文档

## 背景
为了保证 Rango API 的流畅性和高容错性，我们设计了 Rango Worker 系统。该系统主要用于处理 `checkStatus` 接口的请求，因为该接口具有以下特点：
- 用户主动发起请求
- 请求频率高
- 失败率较高
- 失败会影响前端流程

## 系统架构

### Rango 集成流程图
![集成流程图](../../static/img/technical-design/rango-work-design.png)



## 工作流程说明

1. 客户端发起 `checkStatus` 请求到 API 服务
2. API 服务首先检查缓存中是否有结果
3. 如果缓存命中，直接返回缓存结果
4. 如果缓存未命中：
   - API 服务将请求提交给 Worker 处理
   - Worker 负责与 Rango API 通信
   - Worker 将结果缓存
   - 如果请求成功，Worker 将结果保存到数据库
5. 后续相同的请求将直接从缓存获取结果

## 优势

1. 提高系统稳定性
   - 通过 Worker 统一处理请求，避免直接调用 Rango API
   - 减少因 Rango API 不稳定导致的系统故障

2. 提升性能
   - 使用缓存减少重复请求
   - 降低 Rango API 的调用压力

3. 数据可靠性
   - 成功的结果会被持久化到数据库
   - 便于后续查询和统计

4. 用户体验
   - 减少请求失败率
   - 提高响应速度 
