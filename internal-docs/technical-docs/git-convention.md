# Git 提交规范

### 分支策略概述

- **`unstable`（开发主干）**

  - 用于日常开发汇总，所有新特性（Feature）与修复（Bugfix）最终合并目标。
  - 保持相对稳定，但不保证对外发布质量。

- **`staging`（预发布环境）**

  - 汇聚已在 unstable 分支完成测试、手工验证通过的迭代内容。
  - 用于演示、QA 验收，合格后再发布至生产环境。
  - 保持与 unstable 分支同步，定期合并 unstable 分支内容。
  - 受保护分支，仅允许通过 Pull Request 合并。

- **独立分支（Feature/Bugfix）**

  - 从 unstable 拉取，命名格式：feature/ISSUE-123-简要描述 或 bugfix/ISSUE-456-简要描述。
  - 与 JIRA 任务一一对应，始终保持分支名中包含 JIRA 编号。
  - 开发完成后，提交合并请求至 unstable 分支，通过后保留不删除， 并入 staging 后再删除。

### 工作流详细步骤

1. **需求/缺陷领取**

   * 开发者在 JIRA 上将对应卡片从 `TODO` 移至 `IN PROGRESS`。
   * 本地执行：

     ```bash
     git fetch origin
     git checkout unstable
     git pull origin unstable
     git checkout -b feature/ISSUE-123-xxx
     ```

2. **开发阶段**

   * 保持分支与 `unstable` 同步，必要时执行：

     ```bash
     git fetch origin
     git merge origin/unstable
     ```

   * 严格遵循代码规范与分支提交规范（Commit Message 需包含 JIRA 编号、类型、一句话说明）。

3. **功能完成 & 单元测试**

   * 本地自测、撰写或更新对应单元测试。
   * 确保所有测试通过后，推送至远程分支：

     ```bash
     git push origin feature/ISSUE-123-xxx
     ```

   * 在 JIRA 中将卡片移动至 `IN REVIEW`。

4. **代码评审 & 合并至 `unstable`**

   * 部分项目需要发起 Merge Request / Pull Request，指向 `unstable` 分支。
   * 评审通过后由审核人合并；若有问题，则重置回 `IN PROGRESS` 并补充完善。
   * **切勿删除分支**，便于回溯与验证以及并入 `staging` 分支。

5. **自动化测试 & 验证**

   * CI/CD 管道在 `unstable` 分支触发，执行集成测试、静态扫描、安全扫描等。
   * 验证通过后，团队负责人或 Release Manager 推送至 `staging`：

     ```bash
     git checkout staging
     git merge origin/unstable
     git push origin staging
     ```

6. **预发布验收 & 上线准备**

   * QA 在 `staging` 环境完成回归测试。
   * 若通过，则在 JIRA 中将卡片置为 `DONE`；否则，按需回退至 `IN PROGRESS` 并修复。

### Commit 信息及版本管理延伸说明

1. **增强可追溯性**

   * 分支名前缀（`feature/`、`bugfix/`）+ JIRA 编号 + 简要描述。
   * 每次提交遵循 “`<type>(<scope>): <subject>`” 规范，例如：

     ```
     feat(auth): ISSUE-123 实现 OAuth2 登录
     fix(api): ISSUE-456 修复 500 错误响应
     ```

2. **标签与版本管理**

   * 在 `staging` 验收完成后，用 Git Tag 标注版本：

     ```bash
     git tag -a v1.2.3 -m "Release v1.2.3"
     git push origin v1.2.3
     ```

   * Tag 可与 JIRA Release 版本号保持一致，便于回滚与发布日志生成。

3. **保护分支策略**

   * `staging` 已开启分支强制评审、CI 通过、签名提交等保护策略。
   * 禁止直接推送，所有变更必须经由 Pull Request。

4. **持续集成与质量保障**

   * 引入静态代码分析（ESLint、SonarQube）、单元/集成测试、依赖安全扫描（Dependabot）。
   * CI 失败即阻断合并，确保 `staging` 始终处于可发布状态。

5. **文档与自动化**

   * 对常见分支操作编写脚本或 Git Hooks，自动校验分支命名、提交规范。
   * 在 README 或 Notion 文档中集成可视化流程图，帮助其他开发员快速上手。
