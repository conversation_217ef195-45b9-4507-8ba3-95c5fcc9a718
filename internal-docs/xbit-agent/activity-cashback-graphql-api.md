# Activity Cashback GraphQL API Documentation

## Overview

This document provides comprehensive documentation for the Activity Cashback system GraphQL APIs, including user and admin flows, API endpoints, and data structures. This is the most up-to-date and accurate documentation for the Activity Cashback GraphQL API.

## 🔄 Basic System Flow

### 1. System Initialization
```
User Registration → Auto-create user_tier_info → Assign default Tier 1
```

### 2. Task Completion Flow
```
User views Task Center → Selects task → Completes task → Receives points → Checks tier upgrade → Updates cashback
```

### 3. Cashback Claim Flow
```
User accumulates cashback → Views claimable amount → Submits claim → Processes claim → Receives SOL
```

## API Interfaces

### GraphQL Endpoint
```
POST /api/dex-agent/graphql
```

### Admin API Endpoint
```
POST /api/dex-agent/admin/graphql
```

### GraphQL Playground
```
GET /api/dex-agent/graphql/playground
```

### Health Check
```
GET /api/dex-agent/graphql/ping
GET /api/dex-agent/graphql/healthz
```

## 🔐 Authentication

### JWT Token Authentication
All operations requiring authentication need to include JWT Token in the request header:

```http
Authorization: Bearer <jwt_token>
```

### API Key Authentication (Admin Endpoints)
Admin functions require API Key in the request header:

```http
X-API-Key: <api_key>
```

## Table of Contents

1. [👤 User Activity Cashback APIs](#👤-user-activity-cashback-apis)
   - [User Dashboard](#user-dashboard)
   - [Optimized UI Summary](#optimized-ui-summary)
   - [Task Center](#task-center)
   - [User Tier Information](#user-tier-information)
   - [Task Progress](#task-progress)
   - [Task Completion History](#task-completion-history)
   - [Task Categories](#task-categories)
   - [Get Tasks by Category](#get-tasks-by-category)

2. [🔄 Task Operation Mutations](#🔄-task-operation-mutations)
   - [Complete Task](#complete-task)
   - [Claim Cashback](#claim-cashback)
   - [System Operations](#system-operations)

3. [👨‍💼 Admin Function APIs](#👨‍💼-admin-function-apis)
   - [Task Management](#task-management)
   - [Category Management](#category-management)
   - [Tier Benefit Management](#tier-benefit-management)
   - [Admin Queries](#admin-queries)

4. [🌐 REST API Endpoints](#🌐-rest-api-endpoints)
   - [Public Endpoints](#public-endpoints)
   - [Webhook Endpoints](#webhook-endpoints)
   - [Admin Endpoints](#admin-endpoints)

5. [📊 Key Data Fields and Types](#📊-key-data-fields-and-types)
   - [User Tier Information](#user-tier-information-fields)
   - [Task Progress](#task-progress-fields)
   - [Cashback Claims](#cashback-claims-fields)
   - [Task Types](#task-types-fields)
   - [Tier System](#tier-system-fields)

6. [📝 Error Handling](#📝-error-handling)

7. [🚀 Quick Start](#🚀-quick-start)

---

## 👤 User Activity Cashback APIs

### Queries (Data Retrieval)

#### 1. Dashboard and Summary

**Main Dashboard**
```graphql
query {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        currentTier
        totalPoints
        pointsThisMonth
        claimableCashbackUsd
        tradingVolumeUsd
        activeDaysThisMonth
      }
      tierBenefit {
        tierName
        cashbackPercentage
        benefitsDescription
        tierColor
        tierIcon
      }
      nextTier {
        tierName
        minPoints
      }
      pointsToNextTier
      userRank
    }
  }
}
```

**Optimized UI Summary**
```graphql
query {
  activityCashbackSummary {
    success
    data {
      currentLevel
      currentLevelName
      nextLevel
      nextLevelName
      currentScore
      totalScoreForNextLevel
      scoreRequiredToUpgrade
      progressPercentage
      accumulatedTradingVolumeUsd
      activeLogonDays
      accumulatedCashbackUsd
      claimableCashbackUsd
      claimedCashbackUsd
      currentTierColor
      currentTierIcon
    }
  }
}
```

#### 2. Task Center

```graphql
query {
  taskCenter {
    success
    data {
      categories {
        category {
          id
          name
          displayName
          icon
        }
        tasks {
          task {
            id
            name
            description
            points
            frequency
            taskIcon
            buttonText
            externalLink
          }
          progress {
            status
            progressValue
            targetValue
            progressPercentage
            canBeClaimed
            streakCount
          }
        }
      }
      completedToday
      pointsEarnedToday
    }
  }
}
```

#### 3. Task Progress and History

**Detailed Progress**
```graphql
query {
  userTaskProgress {
    success
    data {
      tasks {
        taskId
        taskName
        status
        progressValue
        targetValue
        completionCount
        pointsEarned
        lastCompletedAt
        streakCount
      }
    }
  }
}
```

**Completion History**
```graphql
query {
  taskCompletionHistory(input: {
    taskId: "uuid"
    startDate: "2024-01-01"
    endDate: "2024-01-31"
    limit: 10
    offset: 0
  }) {
    success
    data {
      completions {
        id
        taskName
        pointsAwarded
        completionDate
        verificationData
      }
      totalCount
    }
  }
}
```

#### 4. Other User Queries

**Tier Information**
```graphql
query {
  userTierInfo {
    userId
    currentTier
    totalPoints
    availableCashback
    totalCashbackClaimed
    nextTier
    pointsToNextTier
    lastActivityAt
  }
}
```

**Task Categories**
```graphql
query {
  taskCategories {
    id
    name
    displayName
    description
    icon
    isActive
    sortOrder
  }
}
```

**Get Tasks by Category**
```graphql
query {
  tasksByCategory(categoryName: DAILY) {
    id
    name
    description
    points
    frequency
    isActive
  }
}
```

---

## 🔄 Task Operation Mutations

### 1. Complete Task

```graphql
mutation {
  completeTask(input: {
    taskId: "task-uuid"
    verificationData: "{\"volume\": 1000.0, \"trade_type\": \"MEME\"}"
  }) {
    success
    message
    pointsAwarded
    tierUpgraded
    newTierLevel
  }
}
```

### 2. Claim Cashback

```graphql
mutation {
  claimCashback(input: {
    amountUsd: 50.0
  }) {
    success
    message
    claimId
    amountUsd
    amountSol
  }
}
```

### 3. System Operations

**Refresh Task List**
```graphql
mutation {
  refreshTaskList
}
```

**Trigger Tier Upgrade Check**
```graphql
mutation {
  triggerTierUpgradeCheck {
    success
    message
    tierUpgraded
    newTierLevel
  }
}
```

---

## 👨‍💼 Admin Function APIs

### Task Management

#### 1. Create Task

```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: {
      en: "Daily Check-in"
      zh: "每日签到"
    }
    description: "Complete daily check-in to earn points"
    frequency: DAILY
    points: 10
    maxCompletions: 1
    resetPeriod: "DAILY"
    actionTarget: "CHECKIN"
    verificationMethod: "AUTOMATIC"
    taskIcon: "📅"
    buttonText: "Check In"
    sortOrder: 1
    isActive: true
  }) {
    id
    name
    points
    isActive
  }
}
```

#### 2. Update Task

```graphql
mutation {
  updateTask(input: {
    id: "task-uuid"
    name: {
      en: "Updated Task Name"
    }
    points: 15
    isActive: true
  }) {
    id
    name
    points
    isActive
  }
}
```

#### 3. Delete Task

```graphql
mutation {
  deleteTask(taskId: "task-uuid")
}
```

### Category Management

#### 1. Create Category

```graphql
mutation {
  createTaskCategory(input: {
    name: DAILY
    displayName: "Daily Tasks"
    description: "Tasks that reset daily"
    icon: "📅"
    sortOrder: 1
  }) {
    id
    name
    displayName
  }
}
```

#### 2. Update Category

```graphql
mutation {
  updateTaskCategory(input: {
    id: "1"
    displayName: "Updated Daily Tasks"
    icon: "⏰"
  }) {
    id
    displayName
    icon
  }
}
```

#### 3. Delete Category

```graphql
mutation {
  deleteTaskCategory(categoryId: "1")
}
```

### Tier Benefit Management

#### 1. Create Tier Benefit

```graphql
mutation {
  createTierBenefit(input: {
    tierLevel: 2
    tierName: "Silver"
    minPoints: 500
    cashbackPercentage: 0.2
    netFee: 0.008
    benefitsDescription: "Silver tier benefits"
    tierColor: "#C0C0C0"
    tierIcon: "🥈"
  }) {
    success
    data {
      id
      tierLevel
      tierName
      cashbackPercentage
    }
  }
}
```

#### 2. Update Tier Benefit

```graphql
mutation {
  updateTierBenefit(input: {
    id: "1"
    tierName: "Updated Silver"
    cashbackPercentage: 0.25
    tierColor: "#B0B0B0"
  }) {
    success
    data {
      id
      tierName
      cashbackPercentage
    }
  }
}
```

#### 3. Delete Tier Benefit

```graphql
mutation {
  deleteTierBenefit(tierBenefitId: "1")
}
```

### Admin Queries

#### 1. Get All Tasks

```graphql
query {
  adminGetAllTasks {
    id
    name
    description
    frequency
    points
    isActive
    category {
      name
      displayName
    }
  }
}
```

#### 2. Task Completion Statistics

```graphql
query {
  adminGetTaskCompletionStats {
    totalTasks
    activeTasks
    totalCompletions
    completionRate
    topTasks {
      taskId
      taskName
      completionCount
    }
  }
}
```

---

## 🌐 REST API Endpoints

### Public Endpoints

```bash
# Health Check
GET /api/activity-cashback/health

# System Status
GET /api/activity-cashback/status
```

### Webhook Endpoints

```bash
# Handle External Events
POST /api/activity-cashback/webhook
Content-Type: application/json

{
  "event_type": "trade_completed",
  "user_id": "user-uuid",
  "data": {
    "volume": 1000.0,
    "trade_type": "MEME",
    "symbol": "BTC/USDT"
  }
}
```

### Admin Endpoints

```bash
# Force Reset Tasks
POST /api/activity-cashback/admin/reset/daily
X-Admin-Token: admin-secret-token

# Recalculate Tiers
POST /api/activity-cashback/admin/recalculate-tiers
X-Admin-Token: admin-secret-token
```

---

## 📊 Key Data Fields and Types

### User Tier Information Fields

| Field | Type | Description |
|-------|------|-------------|
| `currentTier` | Integer | Current tier (1-4) |
| `totalPoints` | Integer | Total accumulated points |
| `pointsThisMonth` | Integer | Points earned this month |
| `claimableCashbackUsd` | Decimal | Claimable cashback (USD) |
| `tradingVolumeUsd` | Decimal | Trading volume (USD) |
| `activeDaysThisMonth` | Integer | Active days this month |
| `cumulativeCashbackUsd` | Decimal | Total cumulative cashback |
| `claimedCashbackUsd` | Decimal | Total claimed cashback |

### Task Progress Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | String | Task status (NOT_STARTED, IN_PROGRESS, COMPLETED, CLAIMED) |
| `progressValue` | Integer | Current progress value |
| `targetValue` | Integer | Target value |
| `progressPercentage` | Float | Completion percentage (0-100) |
| `completionCount` | Integer | Number of completions |
| `pointsEarned` | Integer | Points earned |
| `streakCount` | Integer | Consecutive completion days |
| `lastCompletedAt` | DateTime | Last completion time |

### Cashback Claims Fields

| Field | Type | Description |
|-------|------|-------------|
| `claimType` | String | Claim type (TRADING_CASHBACK, TASK_REWARD, TIER_BONUS, REFERRAL_BONUS) |
| `totalAmountUsd` | Decimal | Total amount (USD) |
| `totalAmountSol` | Decimal | Total amount (SOL) |
| `status` | String | Claim status (PENDING, PROCESSING, COMPLETED, FAILED) |
| `transactionHash` | String | Blockchain transaction hash |
| `claimedAt` | DateTime | Claim submission time |
| `processedAt` | DateTime | Processing completion time |

### Task Types Fields

| Type | Description | Reset Period |
|------|-------------|--------------|
| `DAILY` | Daily reset tasks | Every day at UTC 00:00 |
| `WEEKLY` | Weekly reset tasks | Every Monday at UTC 00:00 |
| `MONTHLY` | Monthly reset tasks | 1st of every month at UTC 00:00 |
| `ONE_TIME` | Can only be completed once | Never resets |
| `UNLIMITED` | Can be completed multiple times | No reset |

### Task Category Fields

| Category | Description | Examples |
|----------|-------------|----------|
| `DAILY` | Daily recurring tasks | Check-in, daily trading |
| `COMMUNITY` | Social media tasks | Follow Twitter, join Telegram |
| `TRADING` | Trading-related tasks | Volume milestones, trading streaks |

### Tier System Fields

| Tier | Min Points | Cashback Rate | Benefits |
|------|------------|---------------|----------|
| Tier 1 | 0+ | 0.1% | Basic benefits |
| Tier 2 | 500+ | 0.2% | Enhanced benefits |
| Tier 3 | 2000+ | 0.3% | Advanced benefits |
| Tier 4 | 10000+ | 0.5% | VIP benefits |

---

## 📝 Error Handling

### Common Error Codes

| Code | Description | Action |
|------|-------------|--------|
| `UNAUTHENTICATED` | Invalid or missing token | Redirect to login |
| `FORBIDDEN` | Insufficient permissions | Show access denied |
| `TASK_NOT_FOUND` | Task does not exist | Show task unavailable |
| `TASK_ALREADY_COMPLETED` | Task already completed | Show completion status |
| `INSUFFICIENT_BALANCE` | Insufficient cashback balance | Show balance error |
| `INVALID_VERIFICATION_DATA` | Invalid task verification | Show verification error |

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "errors": [
    {
      "code": "ERROR_CODE",
      "message": "Detailed error message",
      "field": "field_name"
    }
  ]
}
```

---

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Set environment variables
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=xbit_agent
export DB_USER=postgres
export DB_PASSWORD=your_password
export JWT_SECRET=your_jwt_secret
export ADMIN_TOKEN=your_admin_token
```

### 2. Initialize System

```bash
# Run database migrations
make migrate-up

# Start server
make run
```

### 3. Test APIs

```bash
# Test health check
curl http://localhost:8080/api/activity-cashback/health

# Test GraphQL endpoint
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"query": "query { activityCashbackDashboard { success data { userTierInfo { currentTier totalPoints } } } }"}'
```

---

## 📚 Additional Resources

- [Activity Cashback System Overview](ACTIVITY_CASHBACK_SYSTEM.md)
- [Database Schema Documentation](ACTIVITY_CASHBACK_TABLES.md)
- [API Examples](API_EXAMPLES.md)
- [Admin API Documentation](ADMIN_API_KEY_AUTHENTICATION.md)

---

## 🤝 Support

For technical support or questions about the Activity Cashback API, please contact the development team or refer to the project documentation.

---

## 📋 Complete User Flow Example

### 1. Get User Dashboard
```graphql
query {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        currentTier
        totalPoints
        claimableCashbackUsd
      }
      claimableCashback
    }
  }
}
```

### 2. View Task Center
```graphql
query {
  taskCenter {
    success
    message
    data {
      completedToday
      pointsEarnedToday
      categories {
        category {
          name
          displayName
        }
        tasks {
          task {
            name
            points
            taskType
          }
          progress {
            status
            progressPercentage
            canBeClaimed
          }
        }
      }
    }
  }
}
```

### 3. Complete Task
```graphql
mutation {
  completeTask(input: {
    taskId: "task-uuid-here"
    verificationData: "{\"source\":\"manual\"}"
  }) {
    success
    message
    pointsAwarded
    tierUpgraded
  }
}
```

### 4. Claim Cashback
```graphql
mutation {
  claimCashback(input: {
    amountUsd: 25.50
  }) {
    success
    message
    claimId
    amountUsd
    amountSol
  }
}
```

---

## ⚠️ Important Notes

1. **Authentication Required**: All operations require valid JWT token
2. **Permission Control**: Admin functions require appropriate admin permissions
3. **Data Validation**: Input data undergoes strict validation
4. **Error Handling**: Please handle all possible error scenarios properly
5. **Performance Considerations**: Large data queries should use pagination and limit parameters
6. **Real-time Updates**: Some data may need refreshing to get the latest status