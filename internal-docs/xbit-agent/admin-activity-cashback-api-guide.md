# XBIT Agent Admin Activity Cashback API Guide

## Overview

The XBIT Agent Activity Cashback system provides a comprehensive rewards program where users earn points through various activities (trading, community engagement, daily check-ins) and receive cashback based on their tier level. This guide covers all admin APIs for managing the system.

### System Architecture
- **Tier Benefits**: Define reward tiers with cashback percentages and requirements
- **Categories**: Organize tasks into logical groups (DAILY, COMMUNITY, TRADING)
- **Tasks**: Individual activities users can complete to earn points
- **User Progress**: Track user completion status and points earned

## Authentication

All admin APIs require **API Key authentication** using the `X-API-Key` header.

### Required Headers
```http
X-API-Key: your-internal-api-key
Content-Type: application/json
```

### Admin GraphQL Endpoint
```
POST /api/dex-agent/admin/graphql
```

## Tier Benefits Management

### 1. Get All Tier Benefits

**Query:**
```graphql
query GetAllTierBenefits($input: AllTierBenefitsInput!) {
  adminGetAllTierBenefits(input: $input) {
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      referredIncentivePercentage
      netFee
      benefitsDescription
      tierColor
      tierIcon
      isActive
      createdAt
      updatedAt
    }
    total
    page
    pageSize
    totalPages
  }
}
```

**Variables:**
```json
{
  "input": {
    "page": 1,
    "pageSize": 10,
    "sortBy": "tierLevel",
    "sortOrder": "ASC"
  }
}
```

**Response:**
```json
{
  "data": {
    "adminGetAllTierBenefits": {
      "data": [
        {
          "id": "1",
          "tierLevel": 1,
          "tierName": "Bronze",
          "minPoints": 0,
          "cashbackPercentage": 0.001,
          "referredIncentivePercentage": 0.05,
          "netFee": 0.0005,
          "benefitsDescription": "Entry level benefits",
          "tierColor": "#CD7F32",
          "tierIcon": "🥉",
          "isActive": true,
          "createdAt": "2024-01-01T00:00:00Z",
          "updatedAt": "2024-01-01T00:00:00Z"
        }
      ],
      "total": 5,
      "page": 1,
      "pageSize": 10,
      "totalPages": 1
    }
  }
}
```

### 2. Create Tier Benefit

**Mutation:**
```graphql
mutation CreateTierBenefit($input: CreateTierBenefitInput!) {
  createTierBenefit(input: $input) {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      referredIncentivePercentage
      netFee
      benefitsDescription
      tierColor
      tierIcon
      isActive
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "tierLevel": 2,
    "tierName": "Silver",
    "minPoints": 1000,
    "cashbackPercentage": 0.002,
    "referredIncentivePercentage": 0.05,
    "netFee": 0.0008,
    "benefitsDescription": "Enhanced cashback and lower fees",
    "tierColor": "#C0C0C0",
    "tierIcon": "🥈"
  }
}
```

**Response:**
```json
{
  "data": {
    "createTierBenefit": {
      "success": true,
      "message": "Tier benefit created successfully",
      "data": {
        "id": "2",
        "tierLevel": 2,
        "tierName": "Silver",
        "minPoints": 1000,
        "cashbackPercentage": 0.002,
        "referredIncentivePercentage": 0.05,
        "netFee": 0.0008,
        "benefitsDescription": "Enhanced cashback and lower fees",
        "tierColor": "#C0C0C0",
        "tierIcon": "🥈",
        "isActive": true
      }
    }
  }
}
```

### 3. Update Tier Benefit

**Mutation:**
```graphql
mutation UpdateTierBenefit($input: UpdateTierBenefitInput!) {
  updateTierBenefit(input: $input) {
    success
    message
    data {
      id
      tierLevel
      tierName
      minPoints
      cashbackPercentage
      referredIncentivePercentage
      netFee
      benefitsDescription
      tierColor
      tierIcon
      isActive
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "id": "2",
    "cashbackPercentage": 0.0025,
    "netFee": 0.0007,
    "benefitsDescription": "Updated benefits description"
  }
}
```

### 4. Delete Tier Benefit

**Mutation:**
```graphql
mutation DeleteTierBenefit($id: ID!) {
  deleteTierBenefit(id: $id)
}
```

**Variables:**
```json
{
  "id": "2"
}
```

**Response:**
```json
{
  "data": {
    "deleteTierBenefit": true
  }
}
```

## Task Categories Management

### 1. Get All Categories

**Query:**
```graphql
query GetAllCategories($input: AllTaskCategoriesInput!) {
  adminGetAllCategories(input: $input) {
    data {
      id
      name
      displayName
      description
      icon
      isActive
      sortOrder
      createdAt
      updatedAt
    }
    total
    page
    pageSize
    totalPages
  }
}
```

**Variables:**
```json
{
  "input": {
    "page": 1,
    "pageSize": 10,
    "sortBy": "sortOrder",
    "sortOrder": "ASC"
  }
}
```

### 2. Create Task Category

**Mutation:**
```graphql
mutation CreateTaskCategory($input: CreateTaskCategoryInput!) {
  createTaskCategory(input: $input) {
    id
    name
    displayName
    description
    icon
    isActive
    sortOrder
  }
}
```

**Variables:**
```json
{
  "input": {
    "name": "DAILY",
    "displayName": "Daily Tasks",
    "description": "Tasks that reset daily",
    "icon": "📅",
    "sortOrder": 1
  }
}
```

### 3. Update Task Category

**Mutation:**
```graphql
mutation UpdateTaskCategory($input: UpdateTaskCategoryInput!) {
  updateTaskCategory(input: $input) {
    id
    name
    displayName
    description
    icon
    isActive
    sortOrder
  }
}
```

**Variables:**
```json
{
  "input": {
    "id": "1",
    "displayName": "Updated Daily Tasks",
    "description": "Updated description",
    "isActive": true
  }
}
```

### 4. Delete Task Category

**Mutation:**
```graphql
mutation DeleteTaskCategory($id: ID!) {
  deleteTaskCategory(id: $id)
}
```

## Task Management

### Available Task Types

#### Task Frequencies
- `DAILY`: Resets every day at UTC 00:00
- `ONE_TIME`: Can only be completed once
- `UNLIMITED`: Can be completed multiple times
- `PROGRESSIVE`: Milestone-based progression
- `MANUAL`: Requires manual verification

#### Task Identifiers
- **Daily Tasks**: `DAILY_CHECKIN`, `MEME_TRADE_DAILY`, `PERPETUAL_TRADE_DAILY`, `MARKET_PAGE_VIEW`, `CHECK_MARKET_TRENDS`
- **Community Tasks**: `TWITTER_FOLLOW`, `TWITTER_RETWEET`, `TWITTER_LIKE`, `TELEGRAM_JOIN`, `INVITE_FRIENDS`, `SHARE_REFERRAL`, `SHARE_EARNINGS_CHART`
- **Trading Tasks**: `TRADING_POINTS`

#### Task Categories
- `DAILY`: Daily recurring tasks
- `COMMUNITY`: Social media and referral tasks
- `TRADING`: Trading-related activities

### 1. Get All Tasks

**Query:**
```graphql
query GetAllTasks {
  adminGetAllTasks {
    id
    categoryId
    category {
      id
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    taskIcon
    buttonText
    startDate
    endDate
    sortOrder
    isActive
    createdAt
    updatedAt
  }
}
```

### 2. Create Standard Task

**Mutation:**
```graphql
mutation CreateTask($input: CreateTaskInput!) {
  createTask(input: $input) {
    id
    categoryId
    category {
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    taskIcon
    buttonText
    startDate
    endDate
    sortOrder
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "categoryId": "1",
    "name": {
      "en": "Daily Login Bonus",
      "zh": "每日登录奖励",
      "ja": "デイリーログインボーナス"
    },
    "description": "Login daily to earn points",
    "frequency": "DAILY",
    "taskIdentifier": "DAILY_CHECKIN",
    "points": 10,
    "maxCompletions": 1,
    "resetPeriod": "daily",
    "actionTarget": "login",
    "verificationMethod": "auto",
    "taskIcon": "🎁",
    "buttonText": "Claim",
    "sortOrder": 1
  }
}
```

### 3. Create Consecutive Check-in Task

**Mutation:**
```graphql
mutation CreateConsecutiveCheckinTask($input: CreateConsecutiveCheckinTaskInput!) {
  createConsecutiveCheckinTask(input: $input) {
    id
    categoryId
    category {
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    conditions
    taskIcon
    buttonText
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "categoryId": "1",
    "name": {
      "en": "Continuous check-ins",
      "zh": "连续登陆",
      "vi": "Điểm danh liên tục"
    },
    "description": "Complete consecutive check-ins to earn milestone rewards",
    "milestones": [
      {
        "days": 3,
        "points": 10,
        "name": {
          "en": "3 days",
          "zh": "3天",
          "vi": "3 Ngày"
        },
        "taskIcon": "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/db0b74b7-37bc-4b54-b213-8bd0eb129a7f.svg"
      },
      {
        "days": 7,
        "points": 50,
        "name": {
          "en": "7 days",
          "zh": "7天",
          "vi": "7 Ngày"
        },
        "taskIcon": "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f0d75b8e-4f65-40a1-be49-6151fe05def6.svg"
      },
      {
        "days": 30,
        "points": 100,
        "name": {
          "en": "30 days",
          "zh": "30天",
          "vi": "30 Ngày"
        },
        "taskIcon": "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f94c3a3c-26cb-47a6-8ced-ff8f023e6412.svg"
      }
    ],
    "taskIcon": "checkin-icon",
    "buttonText": "check-in",
    "sortOrder": 1,
    "actionTarget": "homePage",
    "verificationMethod": "AUTO"
  }
}
```

### 4. Create Accumulated MEME Trading Volume Task

**Mutation:**
```graphql
mutation CreateAccumulatedMEMETradingVolumeTask($input: CreateAccumulatedMEMETradingVolumeTaskInput!) {
  createAccumulatedMEMETradingVolumeTask(input: $input) {
    id
    categoryId
    category {
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    conditions
    taskIcon
    buttonText
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "volumeThreshold": 1000.0,
    "points": 500,
    "categoryId": "3",
    "name": {
      "en": "MEME Trading Volume Milestone",
      "zh": "MEME交易量里程碑"
    },
    "description": "Reach $1000 in MEME trading volume",
    "taskIcon": "💰",
    "buttonText": "Trade",
    "sortOrder": 1
  }
}
```

### 5. Update Task

**Mutation:**
```graphql
mutation UpdateTask($input: UpdateTaskInput!) {
  updateTask(input: $input) {
    id
    categoryId
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    description
    frequency
    taskIdentifier
    points
    maxCompletions
    resetPeriod
    conditions
    actionTarget
    verificationMethod
    externalLink
    taskIcon
    buttonText
    startDate
    endDate
    sortOrder
    isActive
  }
}
```

**Variables:**
```json
{
  "input": {
    "id": "task-uuid-here",
    "points": 15,
    "description": "Updated task description",
    "isActive": true,
    "taskIcon": "🎯",
    "buttonText": "Complete"
  }
}
```

### 6. Delete Task

**Mutation:**
```graphql
mutation DeleteTask($taskId: ID!) {
  deleteTask(taskId: $taskId)
}
```

**Variables:**
```json
{
  "taskId": "task-uuid-here"
}
```

## Admin Statistics and Analytics

### 1. Get Task Completion Statistics

**Query:**
```graphql
query GetTaskCompletionStats($input: AdminStatsInput!) {
  adminGetTaskCompletionStats(input: $input) {
    success
    message
    data {
      taskCompletions {
        taskName
        completionCount
      }
      startDate
      endDate
      totalTasks
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z"
  }
}
```

### 2. Get User Activity Statistics

**Query:**
```graphql
query GetUserActivityStats($input: AdminStatsInput!) {
  adminGetUserActivityStats(input: $input) {
    success
    message
    data {
      dailyCompletions {
        date
        completionCount
      }
      startDate
      endDate
    }
  }
}
```

### 3. Get Tier Distribution

**Query:**
```graphql
query GetTierDistribution {
  adminGetTierDistribution {
    success
    message
    data {
      tierLevel
      tierName
      userCount
      percentage
    }
  }
}
```

### 4. Get Top Users by Points

**Query:**
```graphql
query GetTopUsers($limit: Int) {
  adminGetTopUsers(limit: $limit) {
    userId
    currentTier
    totalPoints
    pointsThisMonth
    tradingVolumeUsd
    activeDaysThisMonth
    cumulativeCashbackUsd
    claimableCashbackUsd
    claimedCashbackUsd
    lastActivityDate
    tierUpgradedAt
    monthlyResetAt
  }
}
```

**Variables:**
```json
{
  "limit": 20
}
```

## System Management Operations

### 1. Reset Monthly Tasks

**Mutation:**
```graphql
mutation ResetMonthlyTasks {
  adminResetMonthlyTasks
}
```

### 2. Recalculate All User Tiers

**Mutation:**
```graphql
mutation RecalculateAllUserTiers {
  adminRecalculateAllUserTiers
}
```

### 3. Get User Tier Info

**Query:**
```graphql
query GetUserTierInfo($input: UserTierInfoInput!) {
  userTierInfo(input: $input) {
    userId
    currentTier
    totalPoints
    pointsThisMonth
    tradingVolumeUsd
    activeDaysThisMonth
    cumulativeCashbackUsd
    claimableCashbackUsd
    claimedCashbackUsd
    lastActivityDate
    tierUpgradedAt
    monthlyResetAt
  }
}
```

**Variables:**
```json
{
  "input": {
    "userId": "user-uuid-here"
  }
}
```

## REST API Endpoints

In addition to GraphQL, some admin operations are available via REST endpoints:

### Health Check
```http
GET /api/activity-cashback/health
X-API-Key: your-internal-api-key
```

**Response:**
```json
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### System Status
```http
GET /api/activity-cashback/status
X-API-Key: your-internal-api-key
```

**Response:**
```json
{
  "system": "operational",
  "background_jobs": "running",
  "last_daily_reset": "2024-01-01T00:00:00Z",
  "last_monthly_reset": "2024-01-01T00:00:00Z"
}
```

### Force Task Reset
```http
POST /api/activity-cashback/admin/reset/{type}
X-API-Key: your-internal-api-key
```

**Parameters:**
- `type`: `daily`, `weekly`, or `monthly`

**Response:**
```json
{
  "message": "daily tasks reset completed",
  "affected_users": 1250,
  "reset_time": "2024-01-01T00:00:00Z"
}
```

### Recalculate User Tiers
```http
POST /api/activity-cashback/admin/recalculate-tiers
X-API-Key: your-internal-api-key
```

**Response:**
```json
{
  "message": "tier recalculation completed",
  "processed_users": 1250,
  "tier_changes": 45
}
```

## Data Types and Validation Rules

### Tier Benefit Constraints
- `tierLevel`: Must be unique, positive integer
- `tierName`: Required, max 50 characters
- `minPoints`: Must be >= 0, should increase with tier level
- `cashbackPercentage`: Decimal between 0 and 1 (0% to 100%)
- `netFee`: Decimal between 0 and 1 (0% to 100%)
- `referredIncentivePercentage`: Decimal, typically 0.05 (5%)

### Task Constraints
- `name.en`: Required, max 100 characters
- `points`: Must be >= 0
- `maxCompletions`: NULL for unlimited, positive integer for limited
- `sortOrder`: Integer for display ordering
- `startDate`/`endDate`: Unix timestamps, endDate must be after startDate

### Category Constraints
- `name`: Must be one of: DAILY, COMMUNITY, TRADING
- `displayName`: Required, max 100 characters
- `sortOrder`: Integer for display ordering

## Error Handling

### Common Error Responses

#### Authentication Error
```json
{
  "errors": [
    {
      "message": "admin access required: invalid or missing API key",
      "extensions": {
        "code": "UNAUTHENTICATED"
      }
    }
  ]
}
```

#### Validation Error
```json
{
  "errors": [
    {
      "message": "validation failed",
      "extensions": {
        "code": "BAD_USER_INPUT",
        "details": {
          "tierLevel": "tier level must be unique",
          "minPoints": "minimum points must be non-negative"
        }
      }
    }
  ]
}
```

#### Not Found Error
```json
{
  "errors": [
    {
      "message": "tier benefit not found",
      "extensions": {
        "code": "NOT_FOUND",
        "id": "123"
      }
    }
  ]
}
```

#### Business Logic Error
```json
{
  "errors": [
    {
      "message": "cannot delete tier benefit: users exist at this tier level",
      "extensions": {
        "code": "BUSINESS_LOGIC_ERROR",
        "affected_users": 25
      }
    }
  ]
}
```

## Practical Examples

### Example 1: Setting Up a Complete Tier System

```graphql
# Step 1: Create tier benefits
mutation CreateBronzeTier {
  createTierBenefit(input: {
    tierLevel: 1
    tierName: "Bronze"
    minPoints: 0
    cashbackPercentage: 0.001
    netFee: 0.0005
    benefitsDescription: "Entry level with basic rewards"
    tierColor: "#CD7F32"
    tierIcon: "🥉"
  }) {
    success
    data { id }
  }
}

mutation CreateSilverTier {
  createTierBenefit(input: {
    tierLevel: 2
    tierName: "Silver"
    minPoints: 1000
    cashbackPercentage: 0.002
    netFee: 0.0008
    benefitsDescription: "Enhanced cashback and reduced fees"
    tierColor: "#C0C0C0"
    tierIcon: "🥈"
  }) {
    success
    data { id }
  }
}

mutation CreateGoldTier {
  createTierBenefit(input: {
    tierLevel: 3
    tierName: "Gold"
    minPoints: 5000
    cashbackPercentage: 0.003
    netFee: 0.001
    benefitsDescription: "Premium benefits with higher cashback"
    tierColor: "#FFD700"
    tierIcon: "🥇"
  }) {
    success
    data { id }
  }
}
```

### Example 2: Creating a Daily Task Set

```graphql
# Step 1: Get category ID for DAILY
query GetDailyCategory {
  adminGetAllCategories(input: {page: 1, pageSize: 10}) {
    data {
      id
      name
      displayName
    }
  }
}

# Step 2: Create daily login task
mutation CreateDailyLogin {
  createTask(input: {
    categoryId: "1"  # Use actual category ID from step 1
    name: {
      en: "Daily Login"
      zh: "每日登录"
    }
    description: "Login daily to earn points"
    frequency: DAILY
    taskIdentifier: DAILY_CHECKIN
    points: 10
    maxCompletions: 1
    resetPeriod: "daily"
    actionTarget: "login"
    verificationMethod: "auto"
    taskIcon: "🎁"
    buttonText: "Claim"
    sortOrder: 1
  }) {
    id
    name { en }
    points
  }
}

# Step 3: Create trading task
mutation CreateTradingTask {
  createTask(input: {
    categoryId: "1"
    name: {
      en: "Complete MEME Trade"
      zh: "完成MEME交易"
    }
    description: "Complete one MEME transaction"
    frequency: DAILY
    taskIdentifier: MEME_TRADE_DAILY
    points: 200
    maxCompletions: 1
    resetPeriod: "daily"
    actionTarget: "memeTrade"
    verificationMethod: "auto"
    taskIcon: "💰"
    buttonText: "Trade"
    sortOrder: 2
  }) {
    id
    name { en }
    points
  }
}
```

### Example 3: Creating Progressive Tasks

```graphql
# Create consecutive check-in task with milestones
mutation CreateConsecutiveCheckin {
  createConsecutiveCheckinTask(input: {
    categoryId: "1"
    name: {
      en: "Continuous check-ins"
      zh: "连续登陆"
      vi: "Điểm danh liên tục"
    }
    description: "Complete consecutive check-ins to earn milestone rewards"
    milestones: [
      {
        days: 3
        points: 10
        name: {
          en: "3 days"
          zh: "3天"
          vi: "3 Ngày"
        }
        taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/db0b74b7-37bc-4b54-b213-8bd0eb129a7f.svg"
      }
      {
        days: 7
        points: 50
        name: {
          en: "7 days"
          zh: "7天"
          vi: "7 Ngày"
        }
        taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f0d75b8e-4f65-40a1-be49-6151fe05def6.svg"
      }
      {
        days: 30
        points: 100
        name: {
          en: "30 days"
          zh: "30天"
          vi: "30 Ngày"
        }
        taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f94c3a3c-26cb-47a6-8ced-ff8f023e6412.svg"
      }
    ]
    taskIcon: "checkin-icon"
    buttonText: "check-in"
    sortOrder: 1
    actionTarget: "homePage"
    verificationMethod: "AUTO"
  }) {
    id
    points
    conditions
    isActive
    category {
      id
      name
      displayName
    }
    name {
      en
      zh
      ja
      hi
      hk
      vi
    }
    categoryId
    taskIdentifier
    frequency
  }
}

# Create volume-based trading task
mutation CreateVolumeTask {
  createAccumulatedMEMETradingVolumeTask(input: {
    volumeThreshold: 10000.0
    points: 1000
    categoryId: "3"  # Trading category
    name: {
      en: "High Volume Trader"
      zh: "高交易量交易者"
    }
    description: "Reach $10,000 in MEME trading volume"
    taskIcon: "🚀"
    buttonText: "Trade"
    sortOrder: 1
  }) {
    id
    name { en }
    conditions
  }
}
```

## Testing Guidelines

### 1. Environment Setup
- Use a dedicated testing environment
- Ensure test database is isolated from production
- Configure test API keys different from production

### 2. Testing Authentication
```bash
# Test with valid API key
curl -X POST \
  -H "X-API-Key: test-api-key" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { adminGetAllTasks { id name { en } } }"}' \
  http://localhost:8080/api/dex-agent/admin/graphql

# Test with invalid API key (should return 401)
curl -X POST \
  -H "X-API-Key: invalid-key" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { adminGetAllTasks { id name { en } } }"}' \
  http://localhost:8080/api/dex-agent/admin/graphql
```

### 3. Testing CRUD Operations
```bash
# Test tier benefit creation
curl -X POST \
  -H "X-API-Key: test-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation CreateTierBenefit($input: CreateTierBenefitInput!) { createTierBenefit(input: $input) { success data { id tierName } } }",
    "variables": {
      "input": {
        "tierLevel": 1,
        "tierName": "Test Tier",
        "minPoints": 0,
        "cashbackPercentage": 0.001,
        "netFee": 0.0005
      }
    }
  }' \
  http://localhost:8080/api/dex-agent/admin/graphql
```

### 4. Testing Validation
- Test with missing required fields
- Test with invalid data types
- Test with duplicate tier levels
- Test with negative points/percentages
- Test with invalid task identifiers

### 5. Testing Business Logic
- Verify tier progression logic
- Test task completion constraints
- Verify point calculation accuracy
- Test reset functionality
- Verify cashback calculations

### 6. Performance Testing
- Test with large datasets
- Monitor query performance
- Test concurrent operations
- Verify memory usage during bulk operations

## Best Practices

### 1. Data Management
- Always validate input data before creating/updating
- Use transactions for related operations
- Implement proper error handling and rollback
- Monitor system performance after changes

### 2. Security
- Never expose API keys in client-side code
- Rotate API keys regularly
- Log all admin operations for audit trails
- Implement rate limiting for admin endpoints

### 3. Testing
- Test all CRUD operations thoroughly
- Verify business logic constraints
- Test error scenarios and edge cases
- Use automated testing for regression prevention

### 4. Monitoring
- Monitor API response times
- Track error rates and types
- Monitor system resource usage
- Set up alerts for critical failures

## Complete Task Creation Guides

Based on the actual task examples in the system, here are detailed guides for creating each type of task to achieve the exact data structure:

### 1. Daily Login Task

```graphql
mutation CreateDailyLogin {
  createTask(input: {
    categoryId: "1"  # DAILY category
    name: {
      en: "Daily Login"
      zh: "每日登录"
      vi: "Đăng nhập hàng ngày"
      hi: "रोज़ाना लॉगिन"
      hk: "每日登入"
    }
    description: "Refresh after UTC 0:00, log in to claim"
    frequency: DAILY
    taskIdentifier: DAILY_CHECKIN
    points: 5
    maxCompletions: null
    resetPeriod: "DAILY"
    conditions: null
    actionTarget: "homePage"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/6d8e18a2-e688-46ec-9f19-ef00918028ba.svg"
    buttonText: "view"
    sortOrder: 1
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    points
    isActive
  }
}
```

### 2. MEME Trading Daily Task

```graphql
mutation CreateMemeTradeDaily {
  createTask(input: {
    categoryId: "1"  # DAILY category
    name: {
      en: "Complete one meme transaction"
      zh: "完成一笔 Meme 交易"
      vi: "Hoàn tất một giao dịch meme"
      hi: "एक मीम लेनदेन पूरा करें"
      hk: "完成一筆 Meme 交易"
    }
    description: "Complete one MEME transaction between UTC 0:00-23:59"
    frequency: DAILY
    taskIdentifier: MEME_TRADE_DAILY
    points: 200
    maxCompletions: null
    resetPeriod: "DAILY"
    conditions: "{\"required_trade_count\": 1}"
    actionTarget: "memeTrade"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/7ff80176-ca7d-4777-ad42-c8a8eeaf949a.svg"
    buttonText: "trade"
    sortOrder: 2
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    conditions
    actionTarget
  }
}
```

### 3. Contract Trading Daily Task

```graphql
mutation CreateContractTradeDaily {
  createTask(input: {
    categoryId: "1"  # DAILY category
    name: {
      en: "Complete one contract transaction"
      zh: "完成一笔合约交易"
      vi: "Hoàn tất một giao dịch hợp đồng"
      hi: "एक कॉन्ट्रैक्ट लेनदेन पूरा करें"
      hk: "完成一筆合約交易"
    }
    description: "Complete one contract opening or closing between UTC 0:00-23:59"
    frequency: DAILY
    taskIdentifier: PERPETUAL_TRADE_DAILY
    points: 200
    maxCompletions: null
    resetPeriod: "DAILY"
    conditions: "{\"required_trade_count\": 1}"
    actionTarget: "FuturesTrade"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/0a762c2c-0f7d-43f4-90e0-518fcaf77970.svg"
    buttonText: "trade"
    sortOrder: 3
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    conditions
    actionTarget
  }
}
```

### 4. Market Page View Task

```graphql
mutation CreateMarketPageView {
  createTask(input: {
    categoryId: "1"  # DAILY category
    name: {
      en: "View market page"
      zh: "查看市场页面"
      vi: "Xem trang thị trường"
      hi: "बाज़ार पेज देखें"
      hk: "查看市場頁面"
    }
    description: "View the market page to stay updated"
    frequency: DAILY
    taskIdentifier: MARKET_PAGE_VIEW
    points: 5
    maxCompletions: null
    resetPeriod: "DAILY"
    conditions: null
    actionTarget: "market"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/45d988b7-33b9-40d4-891b-7d1d07869788.svg"
    buttonText: "view"
    sortOrder: 4
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    actionTarget
  }
}
```

### 5. Check Market Trends Task

```graphql
mutation CreateCheckMarketTrends {
  createTask(input: {
    categoryId: "1"  # DAILY category
    name: {
      en: "Check Market Trends"
      zh: "查看市场趋势"
      vi: "Xem xu hướng thị trường"
      hi: "बाज़ार की प्रवृत्तियों की जाँच करें"
      hk: "查看市場趨勢"
    }
    description: "Visited the homepage between UTC 0:00–23:59"
    frequency: DAILY
    taskIdentifier: CHECK_MARKET_TRENDS
    points: 5
    maxCompletions: null
    resetPeriod: "DAILY"
    conditions: null
    actionTarget: "memeHome"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/45d988b7-33b9-40d4-891b-7d1d07869788.svg"
    buttonText: "view"
    sortOrder: 5
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    actionTarget
  }
}
```

### 6. Consecutive Check-in Task (Updated with 3/7/30 days)

```graphql
mutation CreateConsecutiveCheckin {
  createConsecutiveCheckinTask(input: {
    categoryId: "1"  # DAILY category
    name: {
      en: "Continuous check-ins"
      zh: "连续签到"
      vi: "Đăng nhập liên tục"
      hi: "लगातार चेक-इन"
      hk: "連續簽到"
    }
    description: "Complete consecutive check-ins to earn milestone rewards"
    milestones: [
      {
        days: 3
        points: 10
        name: {
          en: "3 days"
          zh: "3天"
          vi: "3 Ngày"
        }
        taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/db0b74b7-37bc-4b54-b213-8bd0eb129a7f.svg"
      }
      {
        days: 7
        points: 50
        name: {
          en: "7 days"
          zh: "7天"
          vi: "7 Ngày"
        }
        taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f0d75b8e-4f65-40a1-be49-6151fe05def6.svg"
      }
      {
        days: 30
        points: 100
        name: {
          en: "30 days"
          zh: "30天"
          vi: "30 Ngày"
        }
        taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f94c3a3c-26cb-47a6-8ced-ff8f023e6412.svg"
      }
    ]
    taskIcon: "checkin-icon"
    buttonText: "check-in"
    sortOrder: 1
    actionTarget: "homePage"
    verificationMethod: "AUTO"
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    conditions
    actionTarget
  }
}
```

**Expected Result Structure:**
```json
{
  "id": "b7f02657-a11d-43e8-9e67-81b43ef91af6",
  "category_id": 1,
  "name": "Continuous check-ins",
  "frequency": "PROGRESSIVE",
  "points": 0,
  "conditions": {
    "consecutive_checkin_milestones": [
      {
        "days": 3,
        "points": 10,
        "name": {"en": "3 days", "zh": "3天", "vi": "3 Ngày"},
        "task_icon": "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/db0b74b7-37bc-4b54-b213-8bd0eb129a7f.svg"
      },
      {
        "days": 7,
        "points": 50,
        "name": {"en": "7 days", "zh": "7天", "vi": "7 Ngày"},
        "task_icon": "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f0d75b8e-4f65-40a1-be49-6151fe05def6.svg"
      },
      {
        "days": 30,
        "points": 100,
        "name": {"en": "30 days", "zh": "30天", "vi": "30 Ngày"},
        "task_icon": "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/f94c3a3c-26cb-47a6-8ced-ff8f023e6412.svg"
      }
    ]
  },
  "action_target": "homePage",
  "task_identifier": "CONSECUTIVE_CHECKIN_CONFIGURABLE"
}
```

### 7. Twitter Follow Task (Community)

```graphql
mutation CreateTwitterFollow {
  createTask(input: {
    categoryId: "2"  # COMMUNITY category
    name: {
      en: "Follow on Twitter"
      zh: "关注 Twitter"
      vi: "Theo dõi trên Twitter"
      hi: "ट्विटर पर फॉलो करें"
      hk: "關注 Twitter"
    }
    description: "Points awarded 2 minutes after clicking"
    frequency: ONE_TIME
    taskIdentifier: TWITTER_FOLLOW
    points: 50
    maxCompletions: null
    resetPeriod: "NEVER"
    conditions: null
    actionTarget: null
    verificationMethod: "CLICK_VERIFY"
    externalLink: "https://x.com/intent/follow?screen_name=XBITDEX"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/99c19c61-7a10-4a3f-89be-2f86e6b8cc3e.svg"
    buttonText: "follow"
    sortOrder: 1
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    externalLink
    verificationMethod
  }
}
```

### 8. Twitter Retweet Task (Community)

```graphql
mutation CreateTwitterRetweet {
  createTask(input: {
    categoryId: "2"  # COMMUNITY category
    name: {
      en: "Retweet"
      zh: "转发推文"
      vi: "Chia sẻ lại Tweet"
      hi: "ट्वीट को रीट्वीट करें"
      hk: "轉發推文"
    }
    description: "Points awarded 2 minutes after clicking"
    frequency: MANUAL
    taskIdentifier: TWITTER_RETWEET
    points: 10
    maxCompletions: null
    resetPeriod: "NEVER"
    conditions: null
    actionTarget: null
    verificationMethod: "CLICK_VERIFY"
    externalLink: "https://x.com/intent/retweet?tweet_id=19522412291666035139"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/83bf60e4-3856-47ea-9b0f-b1cc838222fb.svg"
    buttonText: "share"
    sortOrder: 2
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    externalLink
  }
}
```

### 9. Twitter Like Task (Community)

```graphql
mutation CreateTwitterLike {
  createTask(input: {
    categoryId: "2"  # COMMUNITY category
    name: {
      en: "Like Tweet"
      zh: "点赞推文"
      vi: "Thích Tweet"
      hi: "ट्वीट को लाइक करें"
      hk: "點讚推文"
    }
    description: "Points awarded 2 minutes after clicking"
    frequency: MANUAL
    taskIdentifier: TWITTER_LIKE
    points: 10
    maxCompletions: null
    resetPeriod: "NEVER"
    conditions: null
    actionTarget: null
    verificationMethod: "CLICK_VERIFY"
    externalLink: "https://x.com/intent/like?tweet_id=19522412291666035139"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/7107e1d2-1979-4f64-8506-efdefbb3368b.svg"
    buttonText: "view"
    sortOrder: 3
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    externalLink
  }
}
```

### 10. Telegram Join Task (Community)

```graphql
mutation CreateTelegramJoin {
  createTask(input: {
    categoryId: "2"  # COMMUNITY category
    name: {
      en: "Join Telegram"
      zh: "加入 Telegram"
      vi: "Tham gia Telegram"
      hi: "टेलीग्राम में शामिल हों"
      hk: "加入 Telegram"
    }
    description: "Points awarded 2 minutes after clicking"
    frequency: ONE_TIME
    taskIdentifier: TELEGRAM_JOIN
    points: 30
    maxCompletions: null
    resetPeriod: "NEVER"
    conditions: null
    actionTarget: null
    verificationMethod: "CLICK_VERIFY"
    externalLink: "https://t.me/xbit_dex"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/0dff0d28-a8cc-4507-aa18-4456913c529a.svg"
    buttonText: "join"
    sortOrder: 4
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    externalLink
  }
}
```

### 11. Invite Friends Task (Community)

```graphql
mutation CreateInviteFriends {
  createTask(input: {
    categoryId: "2"  # COMMUNITY category
    name: {
      en: "Invite Friends"
      zh: "邀请好友"
      vi: "Mời bạn bè"
      hi: "मित्रों को आमंत्रित करें"
      hk: "邀請好友"
    }
    description: "Earn 100 points for each friend you invite who makes their first transaction on the platform. No limit on completions."
    frequency: UNLIMITED
    taskIdentifier: INVITE_FRIENDS
    points: 100
    maxCompletions: null
    resetPeriod: "NEVER"
    conditions: null
    actionTarget: "InvitationPage"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/d1aa59d8-19fc-4d58-93f2-7b0c049bdc58.svg"
    buttonText: "share"
    sortOrder: 5
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    actionTarget
  }
}
```

### 12. Share Earnings Chart Task (Community)

```graphql
mutation CreateShareEarningsChart {
  createTask(input: {
    categoryId: "2"  # COMMUNITY category
    name: {
      en: "Share Earnings Chart"
      zh: "分享收益图表"
      vi: "Chia sẻ biểu đồ thu nhập"
      hi: "कमाई चार्ट साझा करें"
      hk: "分享收益圖表"
    }
    description: "Trigger by sharing earnings chart. Can be completed multiple times, but points are awarded only once per day."
    frequency: UNLIMITED
    taskIdentifier: SHARE_EARNINGS_CHART
    points: 10
    maxCompletions: null
    resetPeriod: "DAILY"
    conditions: null
    actionTarget: "FuturesTrade"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/7ea9472b-a7a7-4736-8b78-16c74fb4a5cb.svg"
    buttonText: "share"
    sortOrder: 6
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    resetPeriod
  }
}
```

### 13. Trading Points Task (Trading)

```graphql
mutation CreateTradingPoints {
  createTask(input: {
    categoryId: "3"  # TRADING category
    name: {
      en: "Trading Points"
      zh: "交易积分"
      vi: "Điểm giao dịch"
      hi: "ट्रेडिंग पॉइंट्स"
      hk: "交易積分"
    }
    description: "1~99 = 1 point, 100~499 = 5 points, 500~2999 = 12 points, 3000~9999 = 25 points, 10000+ = 40 points. Contract trading volume is calculated based on (position/95)"
    frequency: UNLIMITED
    taskIdentifier: TRADING_POINTS
    points: 0  # Variable points based on volume
    maxCompletions: null
    resetPeriod: "NEVER"
    conditions: null
    actionTarget: "FuturesTrade"
    verificationMethod: "AUTO"
    externalLink: null
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/3a8ba510-34ba-440d-8f58-ccd3c85f5b81.svg"
    buttonText: "trade"
    sortOrder: 1
  }) {
    id
    name { en zh vi hi hk }
    taskIdentifier
    frequency
    points
    actionTarget
  }
}
```

### 14. Accumulated MEME Trading Volume Tasks (Trading)

#### $5 Volume Task
```graphql
mutation CreateMemeVolume5 {
  createAccumulatedMEMETradingVolumeTask(input: {
    volumeThreshold: 5.0
    points: 50
    categoryId: "3"  # TRADING category
    name: {
      en: "Cumulative MEME trading volume $5"
      zh: "MEME累计交易量5美元"
      vi: "Khối lượng giao dịch MEME tích lũy $5"
    }
    description: "Reach Target"
    actionTarget: "memeTrade"
    verificationMethod: "AUTO"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/3a8ba510-34ba-440d-8f58-ccd3c85f5b81.svg"
    buttonText: "trade"
    sortOrder: 1
  }) {
    id
    name { en zh vi }
    taskIdentifier
    conditions
    frequency
  }
}
```

**Expected Result Structure:**
```json
{
  "id": "a68d38c5-1578-48fd-974b-d08e3118afce",
  "category_id": 3,
  "name": "Cumulative MEME trading volume $5",
  "frequency": "PROGRESSIVE",
  "points": 50,
  "conditions": {"min_trading_volume": 5},
  "action_target": "memeTrade",
  "task_identifier": "ACCUMULATED_MEME_TRADING_5",
  "reset_period": "NEVER"
}
```

#### $10 Volume Task
```graphql
mutation CreateMemeVolume10 {
  createAccumulatedMEMETradingVolumeTask(input: {
    volumeThreshold: 10.0
    points: 100
    categoryId: "3"
    name: {
      en: "Cumulative MEME trading volume $10"
      zh: "MEME累计交易量10美元"
      vi: "Khối lượng giao dịch MEME tích lũy $10"
    }
    description: "Reach Target"
    actionTarget: "memeTrade"
    verificationMethod: "AUTO"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/3a8ba510-34ba-440d-8f58-ccd3c85f5b81.svg"
    buttonText: "trade"
    sortOrder: 1
  }) {
    id
    name { en zh vi }
    taskIdentifier
    conditions
  }
}
```

#### $100 Volume Task
```graphql
mutation CreateMemeVolume100 {
  createAccumulatedMEMETradingVolumeTask(input: {
    volumeThreshold: 100.0
    points: 1000
    categoryId: "3"
    name: {
      en: "Cumulative MEME trading volume $100"
      zh: "MEME累计交易量100美元"
      vi: "Khối lượng giao dịch MEME tích lũy $100"
    }
    description: "Reach Target"
    actionTarget: "memeTrade"
    verificationMethod: "AUTO"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/3a8ba510-34ba-440d-8f58-ccd3c85f5b81.svg"
    buttonText: "trade"
    sortOrder: 10
  }) {
    id
    name { en zh vi }
    taskIdentifier
    conditions
  }
}
```

#### $500 Volume Task
```graphql
mutation CreateMemeVolume500 {
  createAccumulatedMEMETradingVolumeTask(input: {
    volumeThreshold: 500.0
    points: 5000
    categoryId: "3"
    name: {
      en: "Cumulative MEME trading volume $500"
      zh: "MEME累计交易量500美元"
      vi: "Khối lượng giao dịch MEME tích lũy $500"
    }
    description: "Reach Target"
    actionTarget: "memeTrade"
    verificationMethod: "AUTO"
    taskIcon: "https://pub-bb979889b4544ce2aa20a6863109552e.r2.dev/3a8ba510-34ba-440d-8f58-ccd3c85f5b81.svg"
    buttonText: "trade"
    sortOrder: 50
  }) {
    id
    name { en zh vi }
    taskIdentifier
    conditions
  }
}
```

## Key Field Mappings and Data Structure

### Important Field Mappings:
- **`name_data`**: Automatically populated from the `name` input object
- **`task_identifier`**: Auto-generated based on task type and parameters
- **`frequency`**: Set automatically based on task type:
  - Daily tasks → `DAILY`
  - One-time tasks → `ONE_TIME`
  - Consecutive check-in → `PROGRESSIVE`
  - Volume tasks → `PROGRESSIVE`
  - Unlimited tasks → `UNLIMITED`
- **`reset_period`**: Automatically set based on frequency:
  - `DAILY` → `"DAILY"`
  - `ONE_TIME` → `"NEVER"`
  - `PROGRESSIVE` → `null` or `"NEVER"`
  - `UNLIMITED` → `"NEVER"` or `"DAILY"`

### Conditions Structure:
- **Daily trading tasks**: `{"required_trade_count": 1}`
- **Volume tasks**: `{"min_trading_volume": 10}`
- **Consecutive check-in**: `{"consecutive_checkin_milestones": [...]}`
- **Simple tasks**: `null`

### Action Targets:
- **`homePage`**: Daily login, consecutive check-in
- **`memeTrade`**: MEME trading tasks
- **`FuturesTrade`**: Contract trading, share earnings
- **`market`**: Market page view
- **`memeHome`**: Market trends check
- **`InvitationPage`**: Invite friends
- **`null`**: External link tasks (Twitter, Telegram)

### Verification Methods:
- **`AUTO`**: Automatic verification by system
- **`CLICK_VERIFY`**: Manual verification after clicking external link
- **`MANUAL`**: Manual admin verification

### Task Icons:
All task icons use CDN URLs from `pub-bb979889b4544ce2aa20a6863109552e.r2.dev` domain with specific SVG files for each task type.

This comprehensive guide provides all the necessary information for frontend developers to integrate with the XBIT Agent Activity Cashback admin APIs and create tasks that match the exact data structure shown in the examples. For additional support or questions, refer to the system documentation or contact the backend development team.
