# Activity Cashback System Documentation

## Overview
The Activity Cashback System is a comprehensive reward system that allows users to earn cashback through various activities and tasks. The system includes task management, tier-based benefits, progress tracking, and claim processing.

## Database Tables

### Core Tables

#### 1. `activity_cashback`
**Purpose**: Stores cashback records for trading activities
**Location**: `internal/model/activity_cashback.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `user_address` | varchar(100) | User's wallet address |
| `status` | varchar(20) | Status: 'PENDING_CLAIM' or 'CLAIMED' |
| `affiliate_transaction_id` | bigint | Reference to affiliate transaction |
| `sol_price_usd` | numeric(36,18) | SOL price in USD at time of transaction |
| `cashback_amount_usd` | numeric(36,18) | Cashback amount in USD |
| `cashback_amount_sol` | numeric(36,18) | Cashback amount in SOL |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |
| `claimed_at` | timestamptz | When cashback was claimed |

**Indexes**:
- `idx_activity_cashback_user_id`
- `idx_activity_cashback_user_address`
- `idx_activity_cashback_status`
- `idx_activity_cashback_affiliate_transaction_id`

#### 2. `activity_cashback_claims`
**Purpose**: Manages cashback claim requests and processing
**Location**: `internal/model/activity_cashback_claim.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `claim_type` | varchar(50) | Type: 'TRADING_CASHBACK', 'TASK_REWARD', 'TIER_BONUS', 'REFERRAL_BONUS' |
| `total_amount_usd` | numeric(38,6) | Total claim amount in USD |
| `total_amount_sol` | numeric(38,6) | Total claim amount in SOL |
| `transaction_hash` | varchar(255) | Blockchain transaction hash |
| `status` | varchar(20) | Status: 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED' |
| `claimed_at` | timestamptz | When claim was initiated |
| `processed_at` | timestamptz | When claim was processed |
| `metadata` | jsonb | Additional claim metadata |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |

**Indexes**:
- `idx_activity_cashback_claims_user_id`

### Task Management Tables

#### 3. `task_categories`
**Purpose**: Categorizes different types of tasks
**Location**: `internal/model/task_category.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | bigserial | Primary key, auto-increment |
| `name` | varchar(50) | Unique category name |
| `display_name` | varchar(100) | Human-readable category name |
| `description` | text | Category description |
| `icon` | varchar(255) | Icon URL or identifier |
| `sort_order` | bigint | Display order (default: 0) |
| `is_active` | boolean | Whether category is active (default: true) |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |

**Constraints**:
- Unique constraint on `name`

#### 4. `activity_tasks`
**Purpose**: Defines available tasks for users
**Location**: `internal/model/activity_task.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `category_id` | bigint | Foreign key to task_categories |
| `name` | varchar(100) | Task name |
| `description` | text | Task description |
| `task_type` | varchar(20) | Type: 'DAILY', 'ONE_TIME', 'UNLIMITED', 'PROGRESSIVE', 'MANUAL_UPDATE' |
| `frequency` | varchar(20) | Frequency: 'DAILY', 'ONE_TIME', 'UNLIMITED', 'PROGRESSIVE', 'MANUAL' |
| `task_identifier` | varchar(50) | Unique task identifier |
| `points` | int | Points awarded for completion |
| `max_completions` | int | Maximum completions (NULL for unlimited) |
| `reset_period` | varchar(20) | Reset period: 'DAILY', 'WEEKLY', 'MONTHLY', 'NEVER' |
| `conditions` | jsonb | Task completion conditions |
| `action_target` | varchar(255) | Target action or URL |
| `verification_method` | varchar(50) | Verification method: 'AUTO', 'MANUAL', 'CLICK_VERIFY' |
| `external_link` | varchar(500) | External link for task |
| `is_active` | boolean | Whether task is active (default: true) |
| `start_date` | timestamptz | Task start date |
| `end_date` | timestamptz | Task end date |
| `sort_order` | int | Display order (default: 0) |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |
| `created_by` | uuid | User who created the task |
| `updated_by` | uuid | User who last updated the task |

**Indexes**:
- Index on `task_identifier`

#### 5. `user_task_progress`
**Purpose**: Tracks user progress on tasks
**Location**: `internal/model/user_task_progress.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `task_id` | uuid | Foreign key to activity_tasks table |
| `status` | varchar(20) | Status: 'NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'CLAIMED', 'EXPIRED' |
| `progress_value` | int | Current progress value (default: 0) |
| `target_value` | int | Target value for completion |
| `completion_count` | int | Number of completions (default: 0) |
| `points_earned` | int | Points earned (default: 0) |
| `last_completed_at` | timestamptz | Last completion timestamp |
| `last_reset_at` | timestamptz | Last reset timestamp |
| `streak_count` | int | Consecutive completion streak (default: 0) |
| `metadata` | jsonb | Additional progress metadata |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |

**Indexes**:
- Index on `user_id`
- Index on `task_id`

### Task Completion Tables

#### 6. `daily_task_completions`
**Purpose**: Records daily task completions
**Location**: `internal/model/daily_task_completion.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `task_id` | uuid | Foreign key to activity_tasks table |
| `points_awarded` | int | Points awarded (default: 0) |
| `completion_date` | date | Date of completion |
| `completion_time` | timestamptz | Timestamp of completion |
| `verification_data` | jsonb | Verification data |
| `ip_address` | inet | User's IP address |
| `user_agent` | text | User's browser agent |
| `created_at` | timestamptz | Record creation timestamp |

**Indexes**:
- `idx_daily_completions_user_date`
- `idx_daily_completions_task_date`
- `idx_daily_completions_date`

#### 7. `one_time_task_completions`
**Purpose**: Records one-time task completions
**Location**: `internal/model/one_time_task_completion.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `task_id` | uuid | Foreign key to activity_tasks table |
| `points_awarded` | int | Points awarded (default: 0) |
| `completion_date` | timestamptz | Completion timestamp |
| `verification_data` | jsonb | Verification data |
| `ip_address` | inet | User's IP address |
| `user_agent` | text | User's browser agent |
| `created_at` | timestamptz | Record creation timestamp |

**Indexes**:
- `idx_onetime_completions_user`
- `idx_onetime_completions_task`
- `idx_onetime_completions_date`
- Unique index: `uk_onetime_user_task`

#### 8. `unlimited_task_completions`
**Purpose**: Records unlimited task completions
**Location**: `internal/model/unlimited_task_completion.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `task_id` | uuid | Foreign key to activity_tasks table |
| `sequence_number` | bigint | Sequence number for ordering |
| `points_awarded` | int | Points awarded (default: 0) |
| `completion_date` | timestamptz | Completion timestamp |
| `verification_data` | jsonb | Verification data |
| `ip_address` | inet | User's IP address |
| `user_agent` | text | User's browser agent |
| `created_at` | timestamptz | Record creation timestamp |

**Indexes**:
- `idx_unlimited_completions_user_task`
- `idx_unlimited_completions_sequence`
- `idx_unlimited_completions_date`

#### 9. `progressive_task_completions`
**Purpose**: Records progressive task completions with levels
**Location**: `internal/model/progressive_task_completion.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `task_id` | uuid | Foreign key to activity_tasks table |
| `level_completed` | int | Level that was completed |
| `total_progress` | int | Total progress value (default: 0) |
| `points_awarded` | int | Points awarded (default: 0) |
| `completion_date` | timestamptz | Completion timestamp |
| `verification_data` | jsonb | Verification data |
| `milestone_data` | jsonb | Milestone-specific data |
| `ip_address` | inet | User's IP address |
| `user_agent` | text | User's browser agent |
| `created_at` | timestamptz | Record creation timestamp |

**Indexes**:
- `idx_progressive_completions_user_task`
- `idx_progressive_completions_level`
- `idx_progressive_completions_date`
- Unique index: `uk_progressive_user_task_level`

#### 10. `manual_task_completions`
**Purpose**: Records manual task completions requiring approval
**Location**: `internal/model/manual_task_completion.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | uuid | Primary key, auto-generated |
| `user_id` | uuid | Foreign key to users table |
| `task_id` | uuid | Foreign key to activity_tasks table |
| `points_awarded` | int | Points awarded (default: 0) |
| `completion_date` | timestamptz | Completion timestamp |
| `verification_data` | jsonb | Verification data |
| `approved_by` | uuid | User who approved the completion |
| `approval_date` | timestamptz | Approval timestamp |
| `approval_notes` | text | Approval notes |
| `status` | varchar(20) | Status: 'PENDING', 'APPROVED', 'REJECTED' |
| `ip_address` | inet | User's IP address |
| `user_agent` | text | User's browser agent |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |

**Indexes**:
- `idx_manual_completions_user`
- `idx_manual_completions_task`
- `idx_manual_completions_date`
- `idx_manual_completions_approver`
- `idx_manual_completions_status`

### Tier Management Tables

#### 11. `user_tier_info`
**Purpose**: Tracks user tier information and cashback statistics
**Location**: `internal/model/user_tier_info.go`

| Field | Type | Description |
|-------|------|-------------|
| `user_id` | uuid | Primary key, foreign key to users table |
| `current_tier` | int | Current tier level (default: 1) |
| `total_points` | int | Total points earned (default: 0) |
| `points_this_month` | int | Points earned this month (default: 0) |
| `trading_volume_usd` | numeric(38,2) | Trading volume in USD (default: 0) |
| `active_days_this_month` | int | Active days this month (default: 0) |
| `cumulative_cashback_usd` | numeric(38,6) | Total cashback earned (default: 0) |
| `claimable_cashback_usd` | numeric(38,6) | Claimable cashback amount (default: 0) |
| `claimed_cashback_usd` | numeric(38,6) | Total claimed cashback (default: 0) |
| `last_activity_date` | timestamptz | Last activity date |
| `tier_upgraded_at` | timestamptz | When tier was last upgraded |
| `monthly_reset_at` | timestamptz | Monthly reset timestamp |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |

#### 12. `tier_benefits`
**Purpose**: Defines tier benefits and requirements
**Location**: `internal/model/tier_benefit.go`

| Field | Type | Description |
|-------|------|-------------|
| `id` | bigserial | Primary key, auto-increment |
| `tier_level` | int | Tier level (unique) |
| `tier_name` | varchar(50) | Tier name |
| `min_points` | int | Minimum points required |
| `cashback_percentage` | numeric(5,4) | Cashback percentage |
| `benefits_description` | text | Description of benefits |
| `tier_color` | varchar(7) | Hex color code for UI |
| `tier_icon` | varchar(255) | Tier icon URL |
| `is_active` | boolean | Whether tier is active (default: true) |
| `created_at` | timestamptz | Record creation timestamp |
| `updated_at` | timestamptz | Last update timestamp |

**Constraints**:
- Unique constraint on `tier_level`

## Interfaces

### Service Interfaces
**Location**: `internal/service/activity_cashback/interfaces.go`

#### 1. `TaskManagementServiceInterface`
Manages task CRUD operations and completion logic.

**Key Methods**:
- `CreateTask(ctx, task)` - Create new task
- `UpdateTask(ctx, task)` - Update existing task
- `DeleteTask(ctx, taskID)` - Delete task
- `GetTaskByID(ctx, taskID)` - Get task by ID
- `GetTasksForUser(ctx, userID)` - Get tasks available to user
- `CompleteTask(ctx, userID, taskID, verificationData)` - Complete a task
- `UpdateTaskProgress(ctx, userID, taskID, progressValue)` - Update task progress
- `ClaimTaskReward(ctx, userID, taskID)` - Claim task reward
- `RefreshUserTasks(ctx, userID)` - Refresh user's available tasks
- `ResetDailyTasks(ctx)`, `ResetWeeklyTasks(ctx)`, `ResetMonthlyTasks(ctx)` - Reset tasks
- `VerifyTaskCompletion(ctx, userID, taskID, verificationData)` - Verify task completion

#### 2. `TierManagementServiceInterface`
Manages user tier information and progression.

**Key Methods**:
- `GetUserTierInfo(ctx, userID)` - Get user's tier information
- `CreateUserTierInfo(ctx, userID)` - Create tier info for new user
- `UpdateUserTierInfo(ctx, tierInfo)` - Update tier information
- `AddPoints(ctx, userID, points, source)` - Add points to user
- `GetUserPoints(ctx, userID)` - Get user's total points
- `GetUserRank(ctx, userID)` - Get user's rank
- `CheckTierUpgrade(ctx, userID)` - Check if user can upgrade tier
- `UpgradeUserTier(ctx, userID, newTier)` - Upgrade user's tier
- `AddCashback(ctx, userID, amount)` - Add cashback to user
- `GetClaimableCashback(ctx, userID)` - Get claimable cashback amount
- `ClaimCashback(ctx, userID, amount)` - Claim cashback

#### 3. `TaskProgressServiceInterface`
Manages task progress tracking and completion.

**Key Methods**:
- `GetUserProgress(ctx, userID)` - Get all user progress
- `GetTaskProgress(ctx, userID, taskID)` - Get specific task progress
- `UpdateProgress(ctx, userID, taskID, progressValue)` - Update progress
- `MarkTaskCompleted(ctx, userID, taskID)` - Mark task as completed
- `ResetTaskProgress(ctx, userID, taskID)` - Reset task progress
- `GetCompletedTasks(ctx, userID)` - Get completed tasks
- `GetClaimableTasks(ctx, userID)` - Get claimable tasks

#### 4. `CashbackClaimServiceInterface`
Manages cashback claim processing.

**Key Methods**:
- `CreateClaim(ctx, userID, claimType, amountUSD, amountSOL, metadata)` - Create claim
- `ProcessClaim(ctx, claimID)` - Process claim
- `CompleteClaim(ctx, claimID, transactionHash)` - Complete claim
- `FailClaim(ctx, claimID, errorDetails)` - Mark claim as failed
- `GetUserClaims(ctx, userID, limit, offset)` - Get user's claims
- `GetClaimByID(ctx, claimID)` - Get claim by ID
- `GetPendingClaims(ctx)` - Get pending claims
- `GetUserClaimHistory(ctx, userID, startDate, endDate)` - Get claim history
- `GetTotalClaimedAmount(ctx, userID)` - Get total claimed amount

#### 5. `ActivityCashbackServiceInterface`
Main interface combining all activity cashback services.

**Extends**:
- `TaskManagementServiceInterface`
- `TierManagementServiceInterface`
- `TaskProgressServiceInterface`
- `CashbackClaimServiceInterface`

**Additional Methods**:
- `InitializeUserForActivityCashback(ctx, userID)` - Initialize user for system
- `GetUserDashboard(ctx, userID)` - Get user dashboard data
- `GetTaskCenter(ctx, userID)` - Get task center data
- `RefreshTaskList(ctx, userID)` - Refresh task list

### Repository Interfaces
**Location**: `internal/repo/activity_cashback/interfaces.go`

#### 1. `TaskCategoryRepositoryInterface`
Manages task category operations.

**Key Methods**:
- `Create(ctx, category)` - Create category
- `Update(ctx, category)` - Update category
- `Delete(ctx, id)` - Delete category
- `GetByID(ctx, id)` - Get category by ID
- `GetByName(ctx, name)` - Get category by name
- `GetAll(ctx)` - Get all categories
- `GetActive(ctx)` - Get active categories

#### 2. `ActivityTaskRepositoryInterface`
Manages activity task operations.

**Key Methods**:
- `Create(ctx, task)` - Create task
- `Update(ctx, task)` - Update task
- `Delete(ctx, id)` - Delete task
- `GetByID(ctx, id)` - Get task by ID
- `GetByCategoryID(ctx, categoryID)` - Get tasks by category
- `GetByTaskType(ctx, taskType)` - Get tasks by type
- `GetActive(ctx)` - Get active tasks
- `GetAvailable(ctx, now)` - Get available tasks
- `GetTasksForUser(ctx, userID)` - Get tasks for user
- `GetDailyTasks(ctx)` - Get daily tasks
- `GetCommunityTasks(ctx)` - Get community tasks
- `GetTradingTasks(ctx)` - Get trading tasks

#### 3. `UserTaskProgressRepositoryInterface`
Manages user task progress operations.

**Key Methods**:
- `Create(ctx, progress)` - Create progress record
- `Update(ctx, progress)` - Update progress
- `GetByUserAndTask(ctx, userID, taskID)` - Get progress for user and task
- `GetByUserID(ctx, userID)` - Get all progress for user
- `GetCompletedTasks(ctx, userID)` - Get completed tasks
- `GetClaimableTasks(ctx, userID)` - Get claimable tasks
- `GetTasksNeedingReset(ctx, resetPeriod)` - Get tasks needing reset
- `BulkUpdateStatus(ctx, progressIDs, status)` - Bulk update status

#### 4. `UserTierInfoRepositoryInterface`
Manages user tier information operations.

**Key Methods**:
- `Create(ctx, tierInfo)` - Create tier info
- `Update(ctx, tierInfo)` - Update tier info
- `GetByUserID(ctx, userID)` - Get tier info by user
- `GetByTier(ctx, tier)` - Get users by tier
- `GetUsersNeedingMonthlyReset(ctx)` - Get users needing reset
- `GetUsersEligibleForUpgrade(ctx, minPoints)` - Get users eligible for upgrade
- `GetTopUsersByPoints(ctx, limit)` - Get top users by points
- `GetUserRankByPoints(ctx, userID)` - Get user rank

#### 5. `TierBenefitRepositoryInterface`
Manages tier benefit operations.

**Key Methods**:
- `Create(ctx, benefit)` - Create tier benefit
- `Update(ctx, benefit)` - Update tier benefit
- `GetByTierLevel(ctx, tierLevel)` - Get benefit by tier level
- `GetAll(ctx)` - Get all benefits
- `GetActive(ctx)` - Get active benefits
- `GetNextTier(ctx, currentTier)` - Get next tier
- `GetTierByPoints(ctx, points)` - Get tier by points

#### 6. Task Completion Repository Interfaces

**DailyTaskCompletionRepositoryInterface**:
- `Create(ctx, completion)` - Create daily completion
- `GetByUserID(ctx, userID, limit, offset)` - Get user's daily completions
- `GetByUserAndDate(ctx, userID, date)` - Get completions by date
- `HasUserCompletedTaskToday(ctx, userID, taskID)` - Check if user completed task today

**OneTimeTaskCompletionRepositoryInterface**:
- `Create(ctx, completion)` - Create one-time completion
- `GetByUserAndTask(ctx, userID, taskID)` - Get completion for user and task
- `HasUserCompletedTask(ctx, userID, taskID)` - Check if user completed task

**UnlimitedTaskCompletionRepositoryInterface**:
- `Create(ctx, completion)` - Create unlimited completion
- `GetUserTaskCompletionCount(ctx, userID, taskID)` - Get completion count
- `GetLastCompletionSequence(ctx, userID, taskID)` - Get last sequence number

**ProgressiveTaskCompletionRepositoryInterface**:
- `Create(ctx, completion)` - Create progressive completion
- `GetByUserTaskAndLevel(ctx, userID, taskID, level)` - Get completion by level
- `GetUserTaskMaxLevel(ctx, userID, taskID)` - Get max level achieved
- `GetUserTaskTotalProgress(ctx, userID, taskID)` - Get total progress

**ManualTaskCompletionRepositoryInterface**:
- `Create(ctx, completion)` - Create manual completion
- `Update(ctx, completion)` - Update manual completion
- `GetByStatus(ctx, status, limit, offset)` - Get completions by status
- `GetPendingApprovals(ctx, limit, offset)` - Get pending approvals
- `Approve(ctx, id, approverID, notes)` - Approve completion
- `Reject(ctx, id, approverID, notes)` - Reject completion

#### 7. `ActivityCashbackClaimRepositoryInterface`
Manages activity cashback claim operations.

**Key Methods**:
- `Create(ctx, claim)` - Create claim
- `Update(ctx, claim)` - Update claim
- `GetByUserID(ctx, userID, limit, offset)` - Get user's claims
- `GetPendingClaims(ctx)` - Get pending claims
- `GetClaimsByStatus(ctx, status)` - Get claims by status
- `GetClaimsByType(ctx, claimType)` - Get claims by type
- `GetUserClaimHistory(ctx, userID, startDate, endDate)` - Get claim history
- `GetTotalClaimedAmount(ctx, userID)` - Get total claimed amount
- `BulkUpdateStatus(ctx, claimIDs, status)` - Bulk update status

## Data Types and Enums

### Task Types
- `DAILY` - Daily recurring tasks
- `ONE_TIME` - One-time tasks
- `UNLIMITED` - Unlimited repeatable tasks
- `PROGRESSIVE` - Progressive tasks with levels
- `MANUAL_UPDATE` - Manual update tasks

### Task Frequencies
- `DAILY` - Daily frequency
- `ONE_TIME` - One-time frequency
- `UNLIMITED` - Unlimited frequency
- `PROGRESSIVE` - Progressive frequency
- `MANUAL` - Manual frequency

### Reset Periods
- `DAILY` - Daily reset
- `WEEKLY` - Weekly reset
- `MONTHLY` - Monthly reset
- `NEVER` - Never reset

### Verification Methods
- `AUTO` - Automatic verification
- `MANUAL` - Manual verification
- `CLICK_VERIFY` - Click-to-verify

### Task Statuses
- `NOT_STARTED` - Task not started
- `IN_PROGRESS` - Task in progress
- `COMPLETED` - Task completed
- `CLAIMED` - Task reward claimed
- `EXPIRED` - Task expired

### Claim Types
- `TRADING_CASHBACK` - Trading cashback
- `TASK_REWARD` - Task completion reward
- `TIER_BONUS` - Tier-based bonus
- `REFERRAL_BONUS` - Referral bonus

### Claim Statuses
- `PENDING` - Claim pending
- `PROCESSING` - Claim being processed
- `COMPLETED` - Claim completed
- `FAILED` - Claim failed

### Manual Task Completion Statuses
- `PENDING` - Pending approval
- `APPROVED` - Approved
- `REJECTED` - Rejected

## Database Migrations

### Key Migration Files
1. **20250822045601.sql** - Creates `activity_cashback` table
2. **20250826040042.sql** - Creates `activity_cashback_claims`, `task_categories`, and `activity_tasks` tables
3. **20250828093110.sql** - Additional activity cashback related migrations

### Migration History
- Initial meme cashback system (20250813100811.sql)
- Migration to activity cashback system (20250822045601.sql)
- Addition of claims and task management (20250826040042.sql)
- Further enhancements (20250828093110.sql)

## System Architecture

### Service Layer
- **ActivityCashbackService** - Main service implementing all interfaces
- **TaskManagementService** - Handles task operations
- **TierManagementService** - Manages user tiers
- **TaskProgressService** - Tracks task progress
- **CashbackClaimService** - Processes claims

### Repository Layer
- Repository interfaces define data access contracts
- Implementations handle database operations
- Factory pattern for task completion repositories

### Model Layer
- GORM models with proper relationships
- Custom types for enums and complex data
- JSON serialization for metadata fields

## Key Features

1. **Multi-type Task System** - Supports daily, one-time, unlimited, progressive, and manual tasks
2. **Tier-based Benefits** - User tiers with different cashback rates
3. **Progress Tracking** - Detailed progress tracking with streaks and metadata
4. **Claim Processing** - Comprehensive claim management with status tracking
5. **Verification System** - Multiple verification methods for task completion
6. **Admin Interface** - Admin services for task and tier management
7. **Audit Trail** - Complete audit trail with IP addresses and user agents

## Integration Points

- **User System** - Integrates with existing user management
- **Affiliate System** - Links to affiliate transactions
- **GraphQL API** - Exposes functionality through GraphQL
- **Admin Panel** - Admin interface for management
- **Background Jobs** - Automated task processing and resets
