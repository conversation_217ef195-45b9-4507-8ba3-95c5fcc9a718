#--------------------------------------
# Stage: Compile Apps
#--------------------------------------
FROM node:22.4.0-alpine3.19 AS builder

ENV REFRESHED_AT=2019-12-08

WORKDIR /app

RUN apk update && apk upgrade
RUN apk add build-base bash curl \
    libffi-dev jpeg-dev zlib-dev openssl-dev \
    autoconf automake libc6-compat libjpeg-turbo-dev \
    libpng-dev nasm libtool g++ make git python3

RUN npm install -g node-gyp \
    && rm -rf /var/cache/apk/*


COPY package*.json ./
RUN npm ci
COPY . ./

RUN npm run build

#--------------------------------------
# Stage: Packaging Apps
#--------------------------------------
FROM nginx:1.16-alpine

VOLUME /app
COPY --from=builder /app/build /app/xbit
COPY robots.txt /app/robots.txt

EXPOSE 80

COPY entrypoint.sh /
RUN chmod +x /entrypoint.sh
CMD ["/entrypoint.sh"]
