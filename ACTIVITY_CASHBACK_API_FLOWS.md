# Activity Cashback API Flows & Documentation

## Overview

This document provides comprehensive documentation for the Activity Cashback system APIs, including user and admin flows, API endpoints, and data structures.

## 🔄 Basic System Flow

### 1. System Initialization
```
User Registration → Auto-create user_tier_info → Assign default Tier 1
```

### 2. Task Completion Flow
```
User views Task Center → Selects task → Completes task → Receives points → Checks tier upgrade → Updates cashback
```

### 3. Cashback Claim Flow
```
User accumulates cashback → Views claimable amount → Submits claim → Processes claim → Receives SOL
```

---

## 👤 USER APIs

### Queries (Data Retrieval)

#### 1. Dashboard & Summary

**Main Dashboard**
```graphql
query {
  activityCashbackDashboard {
    success
    message
    data {
      userTierInfo {
        currentTier
        totalPoints
        pointsThisMonth
        claimableCashbackUsd
        tradingVolumeUsd
        activeDaysThisMonth
      }
      tierBenefit {
        tierName
        cashbackPercentage
        benefitsDescription
        tierColor
        tierIcon
      }
      nextTier {
        tierName
        minPoints
      }
      pointsToNextTier
      userRank
    }
  }
}
```

**Optimized Summary for UI**
```graphql
query {
  activityCashbackSummary {
    success
    data {
      currentLevel
      currentLevelName
      nextLevel
      nextLevelName
      currentScore
      totalScoreForNextLevel
      scoreRequiredToUpgrade
      progressPercentage
      accumulatedTradingVolumeUsd
      activeLogonDays
      accumulatedCashbackUsd
      claimableCashbackUsd
      claimedCashbackUsd
      currentTierColor
      currentTierIcon
    }
  }
}
```

#### 2. Task Center

```graphql
query {
  taskCenter {
    success
    data {
      categories {
        category {
          id
          name
          displayName
          icon
        }
        tasks {
          task {
            id
            name
            description
            points
            frequency
            taskIcon
            buttonText
            externalLink
          }
          progress {
            status
            progressValue
            targetValue
            progressPercentage
            canBeClaimed
            streakCount
          }
        }
      }
      completedToday
      pointsEarnedToday
    }
  }
}
```

#### 3. Task Progress & History

**Detailed Progress**
```graphql
query {
  userTaskProgress {
    success
    data {
      tasks {
        taskId
        taskName
        status
        progressValue
        targetValue
        completionCount
        pointsEarned
        lastCompletedAt
        streakCount
      }
    }
  }
}
```

**Completion History**
```graphql
query {
  taskCompletionHistory(input: {
    taskId: "uuid"
    startDate: "2024-01-01"
    endDate: "2024-01-31"
    limit: 10
    offset: 0
  }) {
    success
    data {
      completions {
        id
        taskName
        pointsAwarded
        completionDate
        verificationData
      }
      totalCount
    }
  }
}
```

#### 4. Additional User Queries

**Tier Information**
```graphql
query {
  userTierInfo {
    userId
    currentTier
    totalPoints
    availableCashback
    totalCashbackClaimed
    nextTier
    pointsToNextTier
    lastActivityAt
  }
}
```

**Task Categories**
```graphql
query {
  taskCategories {
    id
    name
    displayName
    description
    icon
    isActive
    sortOrder
  }
}
```

**Tasks by Category**
```graphql
query {
  tasksByCategory(categoryName: DAILY) {
    id
    name
    description
    points
    frequency
    isActive
  }
}
```

### Mutations (Actions)

#### 1. Complete Task

```graphql
mutation {
  completeTask(input: {
    taskId: "task-uuid"
    verificationData: "{\"volume\": 1000.0, \"trade_type\": \"MEME\"}"
  }) {
    success
    message
    pointsAwarded
    tierUpgraded
    newTierLevel
  }
}
```

#### 2. Claim Cashback

```graphql
mutation {
  claimCashback(input: {
    amountUsd: 50.0
  }) {
    success
    message
    claimId
    amountUsd
    amountSol
  }
}
```

#### 3. System Actions

**Refresh Task List**
```graphql
mutation {
  refreshTaskList
}
```

**Trigger Tier Upgrade Check**
```graphql
mutation {
  triggerTierUpgradeCheck {
    success
    message
    tierUpgraded
    newTierLevel
  }
}
```

---

## 👨‍💼 ADMIN APIs

### Task Management

#### 1. Create Task

```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: {
      en: "Daily Check-in"
      zh: "每日签到"
    }
    description: "Complete daily check-in to earn points"
    frequency: DAILY
    points: 10
    maxCompletions: 1
    resetPeriod: "DAILY"
    actionTarget: "CHECKIN"
    verificationMethod: "AUTOMATIC"
    taskIcon: "📅"
    buttonText: "Check In"
    sortOrder: 1
    isActive: true
  }) {
    id
    name
    points
    isActive
  }
}
```

#### 2. Update Task

```graphql
mutation {
  updateTask(input: {
    id: "task-uuid"
    name: {
      en: "Updated Task Name"
    }
    points: 15
    isActive: true
  }) {
    id
    name
    points
    isActive
  }
}
```

#### 3. Delete Task

```graphql
mutation {
  deleteTask(taskId: "task-uuid")
}
```

### Category Management

#### 1. Create Category

```graphql
mutation {
  createTaskCategory(input: {
    name: DAILY
    displayName: "Daily Tasks"
    description: "Tasks that reset daily"
    icon: "📅"
    sortOrder: 1
  }) {
    id
    name
    displayName
  }
}
```

#### 2. Update Category

```graphql
mutation {
  updateTaskCategory(input: {
    id: "1"
    displayName: "Updated Daily Tasks"
    icon: "⏰"
  }) {
    id
    displayName
    icon
  }
}
```

#### 3. Delete Category

```graphql
mutation {
  deleteTaskCategory(categoryId: "1")
}
```

### Tier Management

#### 1. Create Tier Benefit

```graphql
mutation {
  createTierBenefit(input: {
    tierLevel: 2
    tierName: "Silver"
    minPoints: 500
    cashbackPercentage: 0.2
    netFee: 0.008
    benefitsDescription: "Silver tier benefits"
    tierColor: "#C0C0C0"
    tierIcon: "🥈"
  }) {
    success
    data {
      id
      tierLevel
      tierName
      cashbackPercentage
    }
  }
}
```

#### 2. Update Tier Benefit

```graphql
mutation {
  updateTierBenefit(input: {
    id: "1"
    tierName: "Updated Silver"
    cashbackPercentage: 0.25
    tierColor: "#B0B0B0"
  }) {
    success
    data {
      id
      tierName
      cashbackPercentage
    }
  }
}
```

#### 3. Delete Tier Benefit

```graphql
mutation {
  deleteTierBenefit(tierBenefitId: "1")
}
```

### Admin Queries

#### 1. Get All Tasks

```graphql
query {
  adminGetAllTasks {
    id
    name
    description
    frequency
    points
    isActive
    category {
      name
      displayName
    }
  }
}
```

#### 2. Task Completion Statistics

```graphql
query {
  adminGetTaskCompletionStats {
    totalTasks
    activeTasks
    totalCompletions
    completionRate
    topTasks {
      taskId
      taskName
      completionCount
    }
  }
}
```

---

## 🌐 REST API Endpoints

### Public Endpoints

```bash
# Health check
GET /api/activity-cashback/health

# System status
GET /api/activity-cashback/status
```

### Webhook Endpoints

```bash
# Process external events
POST /api/activity-cashback/webhook
Content-Type: application/json

{
  "event_type": "trade_completed",
  "user_id": "user-uuid",
  "data": {
    "volume": 1000.0,
    "trade_type": "MEME",
    "symbol": "BTC/USDT"
  }
}
```

### Admin Endpoints

```bash
# Force reset tasks
POST /api/activity-cashback/admin/reset/daily
X-Admin-Token: admin-secret-token

# Recalculate tiers
POST /api/activity-cashback/admin/recalculate-tiers
X-Admin-Token: admin-secret-token
```

---

## 📊 Key Data Fields & Types

### User Tier Information

| Field | Type | Description |
|-------|------|-------------|
| `currentTier` | Integer | Current tier level (1-4) |
| `totalPoints` | Integer | Total accumulated points |
| `pointsThisMonth` | Integer | Points earned this month |
| `claimableCashbackUsd` | Decimal | Available cashback in USD |
| `tradingVolumeUsd` | Decimal | Trading volume in USD |
| `activeDaysThisMonth` | Integer | Active days this month |
| `cumulativeCashbackUsd` | Decimal | Total cashback earned |
| `claimedCashbackUsd` | Decimal | Total cashback claimed |

### Task Progress

| Field | Type | Description |
|-------|------|-------------|
| `status` | String | Task status (NOT_STARTED, IN_PROGRESS, COMPLETED, CLAIMED) |
| `progressValue` | Integer | Current progress value |
| `targetValue` | Integer | Target value to complete |
| `progressPercentage` | Float | Completion percentage (0-100) |
| `completionCount` | Integer | Number of times completed |
| `pointsEarned` | Integer | Points earned from this task |
| `streakCount` | Integer | Consecutive completion days |
| `lastCompletedAt` | DateTime | Last completion timestamp |

### Cashback Claims

| Field | Type | Description |
|-------|------|-------------|
| `claimType` | String | Type of claim (TRADING_CASHBACK, TASK_REWARD, TIER_BONUS, REFERRAL_BONUS) |
| `totalAmountUsd` | Decimal | Total amount in USD |
| `totalAmountSol` | Decimal | Total amount in SOL |
| `status` | String | Claim status (PENDING, PROCESSING, COMPLETED, FAILED) |
| `transactionHash` | String | Blockchain transaction hash |
| `claimedAt` | DateTime | Claim submission timestamp |
| `processedAt` | DateTime | Processing completion timestamp |

### Task Types

| Type | Description | Reset Period |
|------|-------------|--------------|
| `DAILY` | Tasks that reset daily | Every day at UTC 00:00 |
| `WEEKLY` | Tasks that reset weekly | Every Monday at UTC 00:00 |
| `MONTHLY` | Tasks that reset monthly | 1st of each month at UTC 00:00 |
| `ONE_TIME` | Can only be completed once | Never resets |
| `UNLIMITED` | Can be completed multiple times | No reset |

### Task Categories

| Category | Description | Examples |
|----------|-------------|----------|
| `DAILY` | Daily recurring tasks | Check-in, daily trading |
| `COMMUNITY` | Social media tasks | Follow Twitter, join Telegram |
| `TRADING` | Trading-related tasks | Volume milestones, trading streaks |

### Tier System

| Tier | Min Points | Cashback Rate | Benefits |
|------|------------|---------------|----------|
| Tier 1 | 0+ | 0.1% | Basic benefits |
| Tier 2 | 500+ | 0.2% | Enhanced benefits |
| Tier 3 | 2000+ | 0.3% | Premium benefits |
| Tier 4 | 10000+ | 0.5% | VIP benefits |

---

## 🔐 Authentication

### User Authentication
- **Required**: JWT Bearer token in Authorization header
- **Format**: `Authorization: Bearer <jwt-token>`
- **Scope**: All user operations require valid authentication

### Admin Authentication
- **Required**: Admin token in X-Admin-Token header
- **Format**: `X-Admin-Token: <admin-secret-token>`
- **Scope**: All admin operations require admin authentication

---

## 📝 Error Handling

### Common Error Codes

| Code | Description | Action |
|------|-------------|--------|
| `UNAUTHENTICATED` | Invalid or missing token | Redirect to login |
| `FORBIDDEN` | Insufficient permissions | Show access denied |
| `TASK_NOT_FOUND` | Task doesn't exist | Show task unavailable |
| `TASK_ALREADY_COMPLETED` | Task already completed | Show completion status |
| `INSUFFICIENT_BALANCE` | Not enough cashback to claim | Show balance error |
| `INVALID_VERIFICATION_DATA` | Invalid task verification | Show verification error |

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "errors": [
    {
      "code": "ERROR_CODE",
      "message": "Detailed error message",
      "field": "fieldName"
    }
  ]
}
```

---

## 🚀 Getting Started

### 1. Environment Setup

```bash
# Set environment variables
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=xbit_agent
export DB_USER=postgres
export DB_PASSWORD=your_password
export JWT_SECRET=your_jwt_secret
export ADMIN_TOKEN=your_admin_token
```

### 2. Initialize System

```bash
# Run database migrations
make migrate-up

# Start the server
make run
```

### 3. Test APIs

```bash
# Test health check
curl http://localhost:8080/api/activity-cashback/health

# Test GraphQL endpoint
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"query": "query { activityCashbackDashboard { success data { userTierInfo { currentTier totalPoints } } } }"}'
```

---

## 📚 Additional Resources

- [Activity Cashback System Overview](ACTIVITY_CASHBACK_SYSTEM.md)
- [Database Schema Documentation](ACTIVITY_CASHBACK_TABLES.md)
- [API Examples](API_EXAMPLES.md)
- [Admin API Documentation](ADMIN_API_KEY_AUTHENTICATION.md)

---

## 🤝 Support

For technical support or questions about the Activity Cashback API, please contact the development team or refer to the project documentation.
